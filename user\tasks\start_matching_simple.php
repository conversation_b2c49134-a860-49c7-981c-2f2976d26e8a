<?php
/**
 * Bamboo Simple Direct Start Matching
 * Company: Notepadsly
 * Version: 1.0
 * Description: Direct database start matching - NO API, NO CSRF
 */

// Define app constant
if (!defined('BAMBOO_APP')) {
    define('BAMBOO_APP', true);
}

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set JSON header
header('Content-Type: application/json');

try {
    // Check if user is logged in
    if (!isLoggedIn()) {
        throw new Exception('User not logged in');
    }

    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('POST method required');
    }

    // Get current user
    $current_user = getCurrentUser();
    if (!$current_user) {
        throw new Exception('Failed to get user information');
    }
    $user_id = $current_user['id'];

    // Check user eligibility
    $user = getCurrentUser();
    $user_balance = getUserBalance($user_id);
    $user_vip = getUserVipLevel($user_id);
    $tasks_today = getTasksCompletedToday($user_id);
    $min_balance = getAppSetting('min_wallet_balance_for_orders', 100);

    // Validation checks
    if ($user_balance['balance'] < $min_balance) {
        throw new Exception('Insufficient balance. Minimum required: USDT ' . number_format($min_balance, 2));
    }

    if ($tasks_today >= ($user_vip['max_daily_tasks'] ?? 5)) {
        throw new Exception('Daily task limit reached');
    }

    // Check for existing pending tasks
    $pending_tasks = fetchAll("SELECT id, product_id, amount, commission_earned FROM tasks WHERE user_id = ? AND status IN ('assigned', 'in_progress')", [$user_id]);
    if (!empty($pending_tasks)) {
        $response = [
            'success' => false,
            'message' => 'You have pending tasks that must be submitted first',
            'redirect_to_records' => true,
            'pending_tasks_count' => count($pending_tasks),
            'data' => [
                'pending_tasks' => $pending_tasks
            ]
        ];
        echo json_encode($response);
        exit;
    }

    // Check for negative settings trigger
    $next_task_number = $tasks_today + 1;
    $negative_setting = fetchRow("SELECT * FROM negative_settings WHERE user_id = ? AND trigger_task_number = ? AND is_active = 1 AND is_triggered = 0", [$user_id, $next_task_number]);

    $is_negative_trigger = false;
    $product = null;
    $task_amount = 0;
    $commission_amount = 0;

    if ($negative_setting) {
        // Negative trigger - assign expensive product
        $is_negative_trigger = true;
        $product = fetchRow("SELECT * FROM products WHERE id = ? AND status = 'active'", [$negative_setting['product_id']]);
        
        if (!$product) {
            // Fallback to any expensive product
            $product = fetchRow("SELECT * FROM products WHERE status = 'active' AND price > 1000 ORDER BY RAND() LIMIT 1");
        }
        
        if ($product) {
            $task_amount = $product['price'];
            $commission_amount = $task_amount * ($negative_setting['commission_rate'] / 100);
        }
    } else {
        // Normal task - assign regular product
        $products_sql = "SELECT * FROM products 
                         WHERE status = 'active' AND (min_vip_level <= ? OR min_vip_level IS NULL)
                         ORDER BY RAND() LIMIT 1";
        $product = fetchRow($products_sql, [$user_vip['level'] ?? 1]);
        
        if ($product) {
            $task_amount = $product['price'];
            $commission_amount = $task_amount * 0.05; // 5% commission for normal tasks
        }
    }

    if (!$product) {
        throw new Exception('No products available for assignment');
    }

    // Check if user has enough balance for the task
    if ($user_balance['balance'] < $task_amount) {
        $response = [
            'success' => false,
            'requires_deposit' => true,
            'message' => 'Insufficient balance for this task',
            'data' => [
                'required_amount' => number_format($task_amount, 2),
                'current_balance' => number_format($user_balance['balance'], 2),
                'shortfall' => number_format($task_amount - $user_balance['balance'], 2),
                'product' => $product,
                'is_negative_trigger' => $is_negative_trigger
            ]
        ];
        echo json_encode($response);
        exit;
    }

    // Start database transaction
    $db = getDB();
    $db->beginTransaction();

    try {
        // Create task
        $create_task_sql = "INSERT INTO tasks (user_id, product_id, amount, commission_earned, status, assigned_at, created_at, updated_at) 
                            VALUES (?, ?, ?, ?, 'assigned', NOW(), NOW(), NOW())";
        $task_result = executeQuery($create_task_sql, [$user_id, $product['id'], $task_amount, $commission_amount]);

        if (!$task_result) {
            throw new Exception('Failed to create task');
        }

        $task_id = $db->lastInsertId();

        // Deduct amount from user balance (freeze it)
        $update_balance_sql = "UPDATE users SET 
                               balance = balance - ?,
                               frozen_balance = frozen_balance + ?
                               WHERE id = ?";
        $balance_result = executeQuery($update_balance_sql, [$task_amount, $task_amount, $user_id]);

        if (!$balance_result) {
            throw new Exception('Failed to update user balance');
        }

        // Record transaction
        recordTransaction($user_id, $task_amount, 'task_deduction', 'Amount deducted for task: ' . $product['name'], 'completed');

        // Mark negative setting as triggered if applicable
        if ($is_negative_trigger && $negative_setting) {
            executeQuery("UPDATE negative_settings SET is_triggered = 1, triggered_at = NOW() WHERE id = ?", [$negative_setting['id']]);
        }

        // Commit transaction
        $db->commit();

        // Get updated balance
        $updated_balance = getUserBalance($user_id);

        // Success response
        $response = [
            'success' => true,
            'message' => 'Task assigned successfully via direct database connection',
            'data' => [
                'task_id' => $task_id,
                'product' => $product,
                'task_amount' => number_format($task_amount, 2),
                'commission_amount' => number_format($commission_amount, 2),
                'new_balance' => number_format($updated_balance['balance'], 2),
                'frozen_balance' => number_format($updated_balance['frozen_balance'], 2),
                'is_negative_trigger' => $is_negative_trigger,
                'submission_method' => 'direct_database_simple'
            ]
        ];

        echo json_encode($response);

    } catch (Exception $e) {
        // Rollback transaction
        $db->rollback();
        throw $e;
    }

} catch (Exception $e) {
    // Log error
    logError("Simple direct start matching error: " . $e->getMessage());
    
    // Error response
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'error_code' => 'SIMPLE_START_MATCHING_ERROR',
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    http_response_code(400);
    echo json_encode($response);
}
?>
