/**
 * Bamboo Task Submission System JavaScript
 * Handles the core task submission workflow
 */

const TaskSystem = {
    config: {
        baseUrl: '',
        csrfToken: '',
        activeTask: null,
        canStartTask: false
    },

    init: function(options) {
        this.config = { ...this.config, ...options };
        this.bindEvents();
        this.loadActiveTask();
    },

    bindEvents: function() {
        // Start Matching button
        $('#startMatchingBtn').on('click', () => {
            this.startMatching();
        });

        // Submit Task button
        $(document).on('click', '#submitTaskBtn', () => {
            this.submitTask();
        });

        // Close Task button
        $(document).on('click', '#closeTaskBtn', () => {
            this.closeTask();
        });

        // Deposit button (for negative balance scenarios)
        $(document).on('click', '#depositBtn', () => {
            window.location.href = this.config.baseUrl + 'user/deposit/deposit.php';
        });
    },

    // Helper function to get correct image URL
    getImageUrl: function(imageUrl) {
        if (!imageUrl) return '';
        // If it's already a full URL, use it as is
        if (imageUrl.startsWith('http')) return imageUrl;
        // If it starts with /, it's a relative path from root
        if (imageUrl.startsWith('/')) return this.config.baseUrl.replace(/\/$/, '') + imageUrl;
        // Otherwise, assume it's relative to uploads
        return this.config.baseUrl + 'uploads/' + imageUrl;
    },

    loadActiveTask: function() {
        if (this.config.activeTask) {
            this.displayActiveTask(this.config.activeTask);
        }
    },

    startMatching: function() {
        if (!this.config.canStartTask) {
            this.showError('Cannot start new task. Daily limit reached or other restriction.');
            return;
        }

        this.showLoading();

        $.ajax({
            url: this.config.baseUrl + 'user/tasks/tasks.php',
            method: 'POST',
            data: {
                action: 'start_matching',
                csrf_token: this.config.csrfToken
            },
            dataType: 'json',
            success: (response) => {
                this.hideLoading();
                
                if (response.success) {
                    if (response.requires_deposit) {
                        this.displayDepositRequired(response.task);
                    } else {
                        this.displayActiveTask(response.task);
                        this.updateBalance(response.task.new_balance);
                    }
                } else {
                    this.showError(response.error || 'Failed to start task');
                }
            },
            error: () => {
                this.hideLoading();
                this.showError('Network error occurred. Please try again.');
            }
        });
    },

    submitTask: function() {
        const taskId = $('#activeTaskContainer').data('task-id');
        
        if (!taskId) {
            this.showError('No active task found');
            return;
        }

        this.showLoading();

        $.ajax({
            url: this.config.baseUrl + 'user/tasks/tasks.php',
            method: 'POST',
            data: {
                action: 'submit_task',
                task_id: taskId,
                csrf_token: this.config.csrfToken
            },
            dataType: 'json',
            success: (response) => {
                this.hideLoading();
                
                if (response.success) {
                    this.displayTaskSuccess(response);
                    this.updateBalance(response.new_balance);

                    // NO AUTOMATIC REDIRECT - let user choose where to go
                    // Show success message with navigation options
                    this.showSuccessWithOptions(response);
                } else {
                    this.showError(response.error || 'Failed to submit task');
                }
            },
            error: () => {
                this.hideLoading();
                this.showError('Network error occurred. Please try again.');
            }
        });
    },

    showSuccessWithOptions: function(response) {
        // Show success message with navigation options instead of auto-redirect
        const successHTML = `
            <div class="task-success-options" style="
                background: linear-gradient(135deg, #4CAF50, #45a049);
                color: white;
                padding: 25px;
                border-radius: 15px;
                text-align: center;
                margin: 20px 0;
                box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
            ">
                <h3 style="margin: 0 0 15px 0;">✅ Task Submitted Successfully!</h3>
                <p style="margin: 0 0 20px 0; font-size: 1.1em;">
                    Your task has been completed and profit has been added to your account.
                </p>
                <div style="margin-top: 20px;">
                    <a href="${this.config.baseUrl}user/dashboard/dashboard.php" class="btn btn-light" style="
                        background: white;
                        color: #4CAF50;
                        padding: 12px 25px;
                        border-radius: 8px;
                        text-decoration: none;
                        font-weight: bold;
                        margin: 5px;
                    ">Dashboard</a>
                    <a href="${this.config.baseUrl}user/records/records.php" class="btn btn-outline-light" style="
                        background: transparent;
                        color: white;
                        border: 2px solid white;
                        padding: 12px 25px;
                        border-radius: 8px;
                        text-decoration: none;
                        font-weight: bold;
                        margin: 5px;
                    ">View Records</a>
                    <a href="${this.config.baseUrl}user/tasks/tasks.php" class="btn btn-outline-light" style="
                        background: transparent;
                        color: white;
                        border: 2px solid white;
                        padding: 12px 25px;
                        border-radius: 8px;
                        text-decoration: none;
                        font-weight: bold;
                        margin: 5px;
                    ">More Tasks</a>
                </div>
            </div>
        `;

        // Replace task container with success options
        $('#activeTaskContainer').html(successHTML);
    },

    closeTask: function() {
        const taskId = $('#activeTaskContainer').data('task-id');
        
        if (!taskId) {
            this.showError('No active task found');
            return;
        }

        if (!confirm('Are you sure you want to close this task? You will not receive any profit.')) {
            return;
        }

        this.showLoading();

        $.ajax({
            url: this.config.baseUrl + 'user/tasks/tasks.php',
            method: 'POST',
            data: {
                action: 'close_task',
                task_id: taskId,
                csrf_token: this.config.csrfToken
            },
            dataType: 'json',
            success: (response) => {
                this.hideLoading();
                
                if (response.success) {
                    // Redirect to dashboard
                    window.location.href = this.config.baseUrl + 'user/dashboard/dashboard.php';
                } else {
                    this.showError(response.error || 'Failed to close task');
                }
            },
            error: () => {
                this.hideLoading();
                this.showError('Network error occurred. Please try again.');
            }
        });
    },

    displayActiveTask: function(task) {
        const product = task.product;
        const appraisalNumber = task.appraisal_number || 'APR' + String(task.id).padStart(6, '0');
        
        const productHtml = `
            <div class="product-display-content fade-in">
                ${product.image_url ? `<img src="${this.getImageUrl(product.image_url)}" alt="${product.name}" class="product-image">` : ''}
                <div class="product-name">${product.name}</div>
                <div class="product-price">Price: USDT ${parseFloat(task.amount).toFixed(2)}</div>
                <div class="product-profit">Total Profit: USDT ${parseFloat(task.commission_earned || 0).toFixed(2)}</div>

                <div class="product-details">
                    <div class="detail-item">
                        <div class="detail-label">Created:</div>
                        <div class="detail-value">${new Date().toLocaleString()}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Appraisal:</div>
                        <div class="detail-value">${appraisalNumber}</div>
                    </div>
                </div>
            </div>
        `;

        $('#productDisplay').html(productHtml);
        $('#taskActions').show();
        $('#activeTaskContainer').data('task-id', task.id);
        
        // Hide product grid and show active task
        $('.product-grid-container').hide();
        $('#activeTaskContainer').show();
    },

    displayDepositRequired: function(task) {
        const product = task.product;
        
        const depositHtml = `
            <div class="product-display-content deposit-required fade-in">
                <div class="deposit-message">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Insufficient Balance - Deposit Required
                </div>
                
                ${product.image_url ? `<img src="${this.getImageUrl(product.image_url)}" alt="${product.name}" class="product-image">` : ''}
                <div class="product-name">${product.name}</div>
                <div class="product-price">Required Amount: USDT ${parseFloat(task.amount).toFixed(2)}</div>
                <div class="balance-needed">Additional Needed: USDT ${parseFloat(task.balance_needed).toFixed(2)}</div>
                
                <div class="mt-3">
                    <p class="text-muted">${task.message}</p>
                    <button type="button" class="btn btn-warning btn-lg" id="depositBtn">
                        <i class="bi bi-plus-circle me-2"></i>Deposit Funds
                    </button>
                </div>
            </div>
        `;

        $('#productDisplay').html(depositHtml);
        $('#taskActions').hide();
        $('#activeTaskContainer').data('task-id', task.id);
        
        // Hide product grid and show active task
        $('.product-grid-container').hide();
        $('#activeTaskContainer').show();
    },

    displayTaskSuccess: function(response) {
        const successHtml = `
            <div class="task-success fade-in">
                <i class="bi bi-check-circle fs-1 text-success"></i>
                <h4 class="mt-3">Task Completed Successfully!</h4>
                <div class="mt-3">
                    <div class="mb-2">
                        <strong>Commission Earned:</strong> 
                        <span class="text-success">USDT ${parseFloat(response.commission).toFixed(2)}</span>
                    </div>
                    <div class="mb-2">
                        <strong>Total Refund:</strong> 
                        <span class="text-success">USDT ${parseFloat(response.refund_amount).toFixed(2)}</span>
                    </div>
                    <div class="mb-3">
                        <strong>New Balance:</strong> 
                        <span class="text-primary">USDT ${parseFloat(response.new_balance).toFixed(2)}</span>
                    </div>
                </div>
                <p class="text-muted">Redirecting to dashboard in 3 seconds...</p>
            </div>
        `;

        $('#productDisplay').html(successHtml);
        $('#taskActions').hide();
    },

    updateBalance: function(newBalance) {
        $('#currentBalance').text('USDT ' + parseFloat(newBalance).toFixed(2));
        
        // Update balance in header if it exists
        $('.user-balance').text('USDT ' + parseFloat(newBalance).toFixed(2));
    },

    showLoading: function() {
        $('#loadingModal').modal('show');
    },

    hideLoading: function() {
        $('#loadingModal').modal('hide');
    },

    showError: function(message) {
        const errorHtml = `
            <div class="task-error fade-in">
                <i class="bi bi-exclamation-triangle fs-1"></i>
                <h5 class="mt-3">Error</h5>
                <p class="mb-0">${message}</p>
            </div>
        `;

        // Show error in product display area if it exists
        if ($('#productDisplay').length) {
            $('#productDisplay').html(errorHtml);
        } else {
            // Create a temporary error display
            $('<div class="container"><div class="row"><div class="col-12">' + errorHtml + '</div></div></div>')
                .insertAfter('.task-main .container')
                .delay(5000)
                .fadeOut();
        }
    },

    showSuccess: function(message) {
        const successHtml = `
            <div class="task-success fade-in">
                <i class="bi bi-check-circle fs-1"></i>
                <h5 class="mt-3">Success</h5>
                <p class="mb-0">${message}</p>
            </div>
        `;

        if ($('#productDisplay').length) {
            $('#productDisplay').html(successHtml);
        } else {
            $('<div class="container"><div class="row"><div class="col-12">' + successHtml + '</div></div></div>')
                .insertAfter('.task-main .container')
                .delay(3000)
                .fadeOut();
        }
    }
};

// Export for global use
window.TaskSystem = TaskSystem;
