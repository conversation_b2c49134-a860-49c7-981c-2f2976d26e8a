/**
 * Bamboo User Records - JavaScript
 * Company: Notepadsly
 * Version: 1.0
 */

// Records Manager
const RecordsManager = {
    init: function() {
        this.bindEvents();
        this.initializeComponents();
    },

    bindEvents: function() {
        // Submit task buttons (both old and new classes)
        $(document).on('click', '.submit-task-btn, .submit-task-btn-header', this.showSubmitModal.bind(this));

        // Modal events
        $('#cancelSubmit, .modal-close').on('click', this.hideSubmitModal.bind(this));
        $('#confirmSubmit').on('click', this.submitTask.bind(this));

        // Close modal when clicking outside
        $('#submitTaskModal').on('click', function(e) {
            if (e.target === this) {
                RecordsManager.hideSubmitModal();
            }
        });

        // Keyboard events
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape') {
                RecordsManager.hideSubmitModal();
            }
        });
    },

    initializeComponents: function() {
        // Add loading states and animations
        $('.task-card').each(function(index) {
            $(this).css('animation-delay', (index * 0.1) + 's');
        });
        
        // Initialize tooltips if needed
        this.initializeTooltips();
    },

    initializeTooltips: function() {
        // Add tooltips for better UX
        $('.submit-task-btn, .submit-task-btn-header').attr('title', 'Submit this task to earn commission');
        $('.task-status').each(function() {
            const status = $(this).text().toLowerCase();
            if (status === 'assigned') {
                $(this).attr('title', 'Task is ready for submission');
            } else if (status === 'in progress') {
                $(this).attr('title', 'Task is currently being processed');
            }
        });
    },

    showSubmitModal: function(e) {
        e.preventDefault();
        
        const $button = $(e.currentTarget);
        const taskId = $button.data('task-id');
        const taskAmount = parseFloat($button.data('task-amount'));
        const commission = parseFloat($button.data('commission'));
        const totalReturn = taskAmount + commission;
        
        // Store task ID for submission
        $('#confirmSubmit').data('task-id', taskId);
        
        // Update modal content
        $('#modalTaskAmount').text('USDT ' + taskAmount.toFixed(2));
        $('#modalCommission').text('USDT ' + commission.toFixed(2));
        $('#modalTotalReturn').text('USDT ' + totalReturn.toFixed(2));
        
        // Show modal
        $('#submitTaskModal').fadeIn(300);
        
        // Focus on confirm button
        setTimeout(() => {
            $('#confirmSubmit').focus();
        }, 300);
    },

    hideSubmitModal: function() {
        $('#submitTaskModal').fadeOut(300);
    },

    submitTask: function(e) {
        e.preventDefault();
        
        const $button = $(e.currentTarget);
        const taskId = $button.data('task-id');
        
        if (!taskId) {
            UserApp.showNotification('Invalid task ID', 'error');
            return;
        }
        
        // Disable button and show loading
        $button.prop('disabled', true).addClass('user-loading');
        $button.html('<i class="icon-loading"></i> Submitting...');
        
        // Use simple direct connection - NO API, NO CSRF
        this.submitTaskSimple(taskId, $button);
    },

    updateUIAfterSubmission: function(taskId, responseData) {
        // Remove the submitted task card
        $(`.task-card[data-task-id="${taskId}"]`).fadeOut(500, function() {
            $(this).remove();
            
            // Check if there are any remaining pending tasks
            if ($('.task-card.pending-task').length === 0) {
                // Show no pending tasks message
                $('.pending-tasks-section').fadeOut(500, function() {
                    const noTasksHtml = `
                        <div class="no-pending-tasks user-fade-in">
                            <div class="empty-state">
                                <i class="icon-check-circle"></i>
                                <h3>All Tasks Completed!</h3>
                                <p>You have successfully submitted all pending tasks. You can now start new matching sessions.</p>
                                <a href="../tasks/" class="btn btn-primary">
                                    <i class="icon-tasks"></i>
                                    Start Matching
                                </a>
                            </div>
                        </div>
                    `;
                    $('.pending-tasks-section').after(noTasksHtml);
                });
            } else {
                // Update pending tasks count
                const remainingCount = $('.task-card.pending-task').length;
                $('.task-count').text(`(${remainingCount})`);
            }
        });
        
        // Update balance if provided
        if (responseData && responseData.new_balance) {
            $('.balance-card .stat-value').text('USDT ' + parseFloat(responseData.new_balance).toFixed(2));
            
            // Update global balance
            if (window.UserApp && window.UserApp.config) {
                window.UserApp.config.balance = responseData.new_balance;
            }
        }
        
        // Update completed tasks count
        if (responseData && responseData.tasks_completed !== undefined) {
            const completedText = $('.completed-card .stat-value').text();
            const parts = completedText.split('/');
            if (parts.length === 2) {
                const newCompleted = responseData.tasks_completed;
                const maxTasks = parts[1];
                $('.completed-card .stat-value').text(`${newCompleted}/${maxTasks}`);
            }
        }
    },

    submitTaskSimple: function(taskId, $button) {
        // Direct database connection - NO API, NO CSRF
        $.ajax({
            url: 'submit_task_simple.php',
            method: 'POST',
            data: {
                task_id: taskId
                // NO CSRF TOKEN REQUIRED
            },
            dataType: 'json',
            success: (response) => {
                if (response.success) {
                    // Hide modal
                    this.hideSubmitModal();

                    // Show success notification
                    UserApp.showNotification('Task submitted successfully via direct database!', 'success');

                    // Update UI
                    this.updateUIAfterSubmission(taskId, response.data);

                    // Handle post-submission workflow
                    this.handlePostSubmissionWorkflow(response.data);

                } else {
                    UserApp.showNotification(response.message || 'Failed to submit task', 'error');
                    this.resetSubmitButton($button);
                    this.hideSubmitModal();
                }
            },
            error: (xhr, status, error) => {
                console.error('Simple direct submit error:', {
                    status: xhr.status,
                    statusText: xhr.statusText,
                    responseText: xhr.responseText,
                    error: error
                });

                let errorMessage = 'Database connection failed';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }

                UserApp.showNotification(errorMessage, 'error');
                this.resetSubmitButton($button);
                this.hideSubmitModal();
            }
        });
    },

    resetSubmitButton: function($button) {
        $button.prop('disabled', false).removeClass('user-loading');
        $button.html('<i class="icon-submit"></i> Submit Task');
    },

    handlePostSubmissionWorkflow: function(data) {
        // Handle different workflow scenarios after task submission
        const workflowStatus = data.workflow_status;

        if (workflowStatus === 'daily_complete') {
            // User has completed their daily task limit - NO REDIRECT from Records page
            UserApp.showNotification(`Congratulations! You completed ${data.tasks_completed}/${data.max_daily_tasks} tasks today. You can view your completed tasks below.`, 'success', 8000);

            // Update the page title to reflect completion
            const pageTitle = document.querySelector('.records-page-title h1');
            if (pageTitle) {
                pageTitle.textContent = 'Task Records - Daily Limit Reached';
            }

            // Show a completion banner instead of redirecting
            this.showDailyCompletionBanner(data);

        } else if (workflowStatus === 'negative_trigger_pending') {
            // User has a negative settings trigger for next task - NO REDIRECT from Records page
            UserApp.showNotification('Next task will require additional deposit. You can start matching from the Tasks page when ready.', 'warning', 6000);

        } else if (workflowStatus === 'can_continue') {
            // User can continue normal matching - NO REDIRECT from Records page
            UserApp.showNotification('Excellent! You can start more matching tasks from the Tasks page.', 'success', 4000);

        } else {
            // Default behavior - just show notification, NO REDIRECT from Records page
            UserApp.showNotification('Task completed successfully! Check the Tasks page for new matching opportunities.', 'success', 4000);
        }

        // Update balance display
        if (data.new_balance) {
            this.updateBalanceDisplay(data.new_balance);
        }

        // Update today's profit
        if (data.today_profit) {
            this.updateTodayProfitDisplay(data.today_profit);
        }
    },

    showDailyCompletionBanner: function(data) {
        // Create a completion banner to show instead of redirecting
        const banner = $(`
            <div class="daily-completion-banner" style="
                background: linear-gradient(135deg, #4CAF50, #45a049);
                color: white;
                padding: 20px;
                border-radius: 12px;
                margin: 20px 0;
                text-align: center;
                box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
                animation: slideIn 0.5s ease-out;
            ">
                <h3 style="margin: 0 0 10px 0; font-size: 1.5em;">🎉 Daily Tasks Completed!</h3>
                <p style="margin: 0; font-size: 1.1em;">
                    You've successfully completed ${data.tasks_completed}/${data.max_daily_tasks} tasks today.<br>
                    Total profit earned: USDT ${data.today_profit}
                </p>
                <div style="margin-top: 15px;">
                    <a href="../dashboard/dashboard.php" class="btn btn-light" style="
                        background: white;
                        color: #4CAF50;
                        padding: 10px 20px;
                        border-radius: 6px;
                        text-decoration: none;
                        font-weight: bold;
                        margin-right: 10px;
                    ">View Dashboard</a>
                    <a href="../tasks/tasks.php" class="btn btn-outline-light" style="
                        background: transparent;
                        color: white;
                        border: 2px solid white;
                        padding: 10px 20px;
                        border-radius: 6px;
                        text-decoration: none;
                        font-weight: bold;
                    ">Tasks Page</a>
                </div>
            </div>
        `);

        // Insert banner after the page title
        $('.records-page-title').after(banner);

        // Add CSS animation
        if (!$('#completion-banner-styles').length) {
            $('head').append(`
                <style id="completion-banner-styles">
                    @keyframes slideIn {
                        from { opacity: 0; transform: translateY(-20px); }
                        to { opacity: 1; transform: translateY(0); }
                    }
                </style>
            `);
        }
    },

    updateBalanceDisplay: function(newBalance) {
        // Update balance displays on the page
        $('.balance-amount, .current-balance').text('USDT ' + newBalance);
    },

    updateTodayProfitDisplay: function(todayProfit) {
        // Update today's profit displays on the page
        $('.today-profit, .daily-profit').text('USDT ' + todayProfit);
    },

    // Utility functions
    formatCurrency: function(amount) {
        return 'USDT ' + parseFloat(amount).toFixed(2);
    },

    showLoadingState: function($element, message = 'Loading...') {
        $element.addClass('user-loading').prop('disabled', true);
        if ($element.is('button')) {
            $element.data('original-text', $element.html());
            $element.html(`<i class="icon-loading"></i> ${message}`);
        }
    },

    hideLoadingState: function($element) {
        $element.removeClass('user-loading').prop('disabled', false);
        if ($element.is('button') && $element.data('original-text')) {
            $element.html($element.data('original-text'));
        }
    }
};

// Initialize when document is ready
$(document).ready(function() {
    RecordsManager.init();
    
    // Add smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(e) {
        e.preventDefault();
        const target = $(this.getAttribute('href'));
        if (target.length) {
            $('html, body').animate({
                scrollTop: target.offset().top - 100
            }, 500);
        }
    });
    
    // Add hover effects for task cards
    $('.task-card').hover(
        function() {
            $(this).addClass('hover-effect');
        },
        function() {
            $(this).removeClass('hover-effect');
        }
    );
    
    // Auto-refresh page every 5 minutes to keep data current
    setInterval(function() {
        // Only refresh if there are pending tasks and user is active
        if ($('.task-card.pending-task').length > 0 && document.hasFocus()) {
            console.log('Auto-refreshing records page...');
            window.location.reload();
        }
    }, 5 * 60 * 1000); // 5 minutes
});

// Handle page visibility changes
document.addEventListener('visibilitychange', function() {
    if (!document.hidden && $('.task-card.pending-task').length > 0) {
        // Page became visible and has pending tasks - refresh data
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }
});

// Export for global access
window.RecordsManager = RecordsManager;
