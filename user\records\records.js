/**
 * Bamboo User Records - JavaScript
 * Company: Notepadsly
 * Version: 1.0
 */

// Records Manager
const RecordsManager = {
    init: function() {
        this.bindEvents();
        this.initializeComponents();
    },

    bindEvents: function() {
        // Submit task buttons
        $(document).on('click', '.submit-task-btn', this.showSubmitModal.bind(this));
        
        // Modal events
        $('#cancelSubmit, .modal-close').on('click', this.hideSubmitModal.bind(this));
        $('#confirmSubmit').on('click', this.submitTask.bind(this));
        
        // Close modal when clicking outside
        $('#submitTaskModal').on('click', function(e) {
            if (e.target === this) {
                RecordsManager.hideSubmitModal();
            }
        });
        
        // Keyboard events
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape') {
                RecordsManager.hideSubmitModal();
            }
        });
    },

    initializeComponents: function() {
        // Add loading states and animations
        $('.task-card').each(function(index) {
            $(this).css('animation-delay', (index * 0.1) + 's');
        });
        
        // Initialize tooltips if needed
        this.initializeTooltips();
    },

    initializeTooltips: function() {
        // Add tooltips for better UX
        $('.submit-task-btn').attr('title', 'Submit this task to earn commission');
        $('.task-status').each(function() {
            const status = $(this).text().toLowerCase();
            if (status === 'assigned') {
                $(this).attr('title', 'Task is ready for submission');
            } else if (status === 'in progress') {
                $(this).attr('title', 'Task is currently being processed');
            }
        });
    },

    showSubmitModal: function(e) {
        e.preventDefault();
        
        const $button = $(e.currentTarget);
        const taskId = $button.data('task-id');
        const taskAmount = parseFloat($button.data('task-amount'));
        const commission = parseFloat($button.data('commission'));
        const totalReturn = taskAmount + commission;
        
        // Store task ID for submission
        $('#confirmSubmit').data('task-id', taskId);
        
        // Update modal content
        $('#modalTaskAmount').text('USDT ' + taskAmount.toFixed(2));
        $('#modalCommission').text('USDT ' + commission.toFixed(2));
        $('#modalTotalReturn').text('USDT ' + totalReturn.toFixed(2));
        
        // Show modal
        $('#submitTaskModal').fadeIn(300);
        
        // Focus on confirm button
        setTimeout(() => {
            $('#confirmSubmit').focus();
        }, 300);
    },

    hideSubmitModal: function() {
        $('#submitTaskModal').fadeOut(300);
    },

    submitTask: function(e) {
        e.preventDefault();
        
        const $button = $(e.currentTarget);
        const taskId = $button.data('task-id');
        
        if (!taskId) {
            UserApp.showNotification('Invalid task ID', 'error');
            return;
        }
        
        // Disable button and show loading
        $button.prop('disabled', true).addClass('user-loading');
        $button.html('<i class="icon-loading"></i> Submitting...');
        
        // Try API first, then fallback to direct connection
        this.submitTaskWithFallback(taskId, $button);
    },

    updateUIAfterSubmission: function(taskId, responseData) {
        // Remove the submitted task card
        $(`.task-card[data-task-id="${taskId}"]`).fadeOut(500, function() {
            $(this).remove();
            
            // Check if there are any remaining pending tasks
            if ($('.task-card.pending-task').length === 0) {
                // Show no pending tasks message
                $('.pending-tasks-section').fadeOut(500, function() {
                    const noTasksHtml = `
                        <div class="no-pending-tasks user-fade-in">
                            <div class="empty-state">
                                <i class="icon-check-circle"></i>
                                <h3>All Tasks Completed!</h3>
                                <p>You have successfully submitted all pending tasks. You can now start new matching sessions.</p>
                                <a href="../tasks/" class="btn btn-primary">
                                    <i class="icon-tasks"></i>
                                    Start Matching
                                </a>
                            </div>
                        </div>
                    `;
                    $('.pending-tasks-section').after(noTasksHtml);
                });
            } else {
                // Update pending tasks count
                const remainingCount = $('.task-card.pending-task').length;
                $('.task-count').text(`(${remainingCount})`);
            }
        });
        
        // Update balance if provided
        if (responseData && responseData.new_balance) {
            $('.balance-card .stat-value').text('USDT ' + parseFloat(responseData.new_balance).toFixed(2));
            
            // Update global balance
            if (window.UserApp && window.UserApp.config) {
                window.UserApp.config.balance = responseData.new_balance;
            }
        }
        
        // Update completed tasks count
        if (responseData && responseData.tasks_completed !== undefined) {
            const completedText = $('.completed-card .stat-value').text();
            const parts = completedText.split('/');
            if (parts.length === 2) {
                const newCompleted = responseData.tasks_completed;
                const maxTasks = parts[1];
                $('.completed-card .stat-value').text(`${newCompleted}/${maxTasks}`);
            }
        }
    },

    submitTaskWithFallback: function(taskId, $button) {
        // First try the API
        $.ajax({
            url: '../api/tasks.php',
            method: 'POST',
            data: {
                action: 'submit_task',
                task_id: taskId,
                csrf_token: UserApp.config.csrfToken
            },
            dataType: 'json',
            success: (response) => {
                if (response.success) {
                    this.handleSuccessfulSubmission(response, 'API');
                } else {
                    // API returned error, try direct connection
                    console.log('API returned error, trying direct connection...');
                    this.submitTaskDirect(taskId, $button);
                }
            },
            error: (xhr, status, error) => {
                console.error('API submit error:', {
                    status: xhr.status,
                    statusText: xhr.statusText,
                    responseText: xhr.responseText,
                    error: error
                });

                // API failed, try direct connection
                console.log('API failed, trying direct connection...');
                UserApp.showNotification('API connection failed, trying direct connection...', 'info');
                this.submitTaskDirect(taskId, $button);
            }
        });
    },

    submitTaskDirect: function(taskId, $button) {
        $.ajax({
            url: 'submit_task_direct.php',
            method: 'POST',
            data: {
                task_id: taskId,
                csrf_token: UserApp.config.csrfToken
            },
            dataType: 'json',
            success: (response) => {
                if (response.success) {
                    this.handleSuccessfulSubmission(response, 'Direct');
                } else {
                    UserApp.showNotification(response.message || 'Failed to submit task via direct connection', 'error');
                    this.resetSubmitButton($button);
                    this.hideSubmitModal();
                }
            },
            error: (xhr, status, error) => {
                console.error('Direct submit error:', {
                    status: xhr.status,
                    statusText: xhr.statusText,
                    responseText: xhr.responseText,
                    error: error
                });

                let errorMessage = 'Both API and direct connection failed';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.status === 403) {
                    errorMessage = 'Invalid CSRF token. Please refresh the page.';
                }

                UserApp.showNotification(errorMessage, 'error');
                this.resetSubmitButton($button);
                this.hideSubmitModal();
            }
        });
    },

    handleSuccessfulSubmission: function(response, method) {
        // Hide modal
        this.hideSubmitModal();

        // Show success notification with method info
        const methodText = method === 'Direct' ? ' (via direct connection)' : '';
        UserApp.showNotification('Task submitted successfully' + methodText + '!', 'success');

        // Update UI
        const taskId = $('#confirmSubmit').data('task-id');
        this.updateUIAfterSubmission(taskId, response.data);

        // Handle post-submission workflow
        this.handlePostSubmissionWorkflow(response.data);
    },

    resetSubmitButton: function($button) {
        $button.prop('disabled', false).removeClass('user-loading');
        $button.html('<i class="icon-submit"></i> Submit Task');
    },

    handlePostSubmissionWorkflow: function(data) {
        // Handle different workflow scenarios after task submission
        const workflowStatus = data.workflow_status;

        if (workflowStatus === 'daily_complete') {
            // User has completed their daily task limit
            UserApp.showNotification(`Congratulations! You completed ${data.tasks_completed}/${data.max_daily_tasks} tasks today.`, 'success', 8000);

            setTimeout(() => {
                // Redirect to dashboard
                window.location.href = '../dashboard/dashboard.php';
            }, 4000);

        } else if (workflowStatus === 'negative_trigger_pending') {
            // User has a negative settings trigger for next task
            UserApp.showNotification('Next task will require additional deposit. You can start matching when ready.', 'warning', 6000);

            setTimeout(() => {
                // Check if there are more pending tasks
                if ($('.task-card.pending-task').length <= 1) {
                    // No more pending tasks, redirect to tasks page
                    window.location.href = '../tasks/tasks.php';
                } else {
                    // More pending tasks, reload records page
                    window.location.reload();
                }
            }, 3000);

        } else if (workflowStatus === 'can_continue') {
            // User can continue normal matching
            UserApp.showNotification('Excellent! You can continue with more tasks.', 'success', 4000);

            setTimeout(() => {
                // Check if there are more pending tasks
                if ($('.task-card.pending-task').length <= 1) {
                    // No more pending tasks, redirect to tasks page
                    window.location.href = '../tasks/tasks.php';
                } else {
                    // More pending tasks, reload records page
                    window.location.reload();
                }
            }, 2500);

        } else {
            // Default behavior - reload page or redirect based on pending tasks
            setTimeout(() => {
                if ($('.task-card.pending-task').length <= 1) {
                    window.location.href = '../tasks/tasks.php';
                } else {
                    window.location.reload();
                }
            }, 3000);
        }
    },

    // Utility functions
    formatCurrency: function(amount) {
        return 'USDT ' + parseFloat(amount).toFixed(2);
    },

    showLoadingState: function($element, message = 'Loading...') {
        $element.addClass('user-loading').prop('disabled', true);
        if ($element.is('button')) {
            $element.data('original-text', $element.html());
            $element.html(`<i class="icon-loading"></i> ${message}`);
        }
    },

    hideLoadingState: function($element) {
        $element.removeClass('user-loading').prop('disabled', false);
        if ($element.is('button') && $element.data('original-text')) {
            $element.html($element.data('original-text'));
        }
    }
};

// Initialize when document is ready
$(document).ready(function() {
    RecordsManager.init();
    
    // Add smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(e) {
        e.preventDefault();
        const target = $(this.getAttribute('href'));
        if (target.length) {
            $('html, body').animate({
                scrollTop: target.offset().top - 100
            }, 500);
        }
    });
    
    // Add hover effects for task cards
    $('.task-card').hover(
        function() {
            $(this).addClass('hover-effect');
        },
        function() {
            $(this).removeClass('hover-effect');
        }
    );
    
    // Auto-refresh page every 5 minutes to keep data current
    setInterval(function() {
        // Only refresh if there are pending tasks and user is active
        if ($('.task-card.pending-task').length > 0 && document.hasFocus()) {
            console.log('Auto-refreshing records page...');
            window.location.reload();
        }
    }, 5 * 60 * 1000); // 5 minutes
});

// Handle page visibility changes
document.addEventListener('visibilitychange', function() {
    if (!document.hidden && $('.task-card.pending-task').length > 0) {
        // Page became visible and has pending tasks - refresh data
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }
});

// Export for global access
window.RecordsManager = RecordsManager;
