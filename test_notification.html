<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notification Test</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Our CSS -->
    <link href="user/assets/css/user-master.css" rel="stylesheet">
    
    <style>
        body {
            padding: 2rem;
            background: #f8f9fa;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .btn {
            margin: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Notification Border Test</h1>
        
        <div class="test-section">
            <h3>Test Notifications</h3>
            <button class="btn btn-success" onclick="showNotification('Task submitted successfully!', 'success')">Success Notification</button>
            <button class="btn btn-danger" onclick="showNotification('An error occurred!', 'error')">Error Notification</button>
            <button class="btn btn-warning" onclick="showNotification('Warning message!', 'warning')">Warning Notification</button>
            <button class="btn btn-info" onclick="showNotification('Information message!', 'info')">Info Notification</button>
        </div>
        
        <div class="test-section">
            <h3>Manual Notification Examples</h3>
            
            <div class="user-notification user-notification-success" style="margin-bottom: 1rem;">
                <div class="notification-content">
                    <span class="notification-message">Success notification - should have light green background, NO left border</span>
                </div>
            </div>
            
            <div class="user-notification user-notification-error" style="margin-bottom: 1rem;">
                <div class="notification-content">
                    <span class="notification-message">Error notification - should have light red background, NO left border</span>
                </div>
            </div>
            
            <div class="user-notification user-notification-warning" style="margin-bottom: 1rem;">
                <div class="notification-content">
                    <span class="notification-message">Warning notification - should have light yellow background, NO left border</span>
                </div>
            </div>
            
            <div class="user-notification user-notification-info" style="margin-bottom: 1rem;">
                <div class="notification-content">
                    <span class="notification-message">Info notification - should have light blue background, NO left border</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="user-notifications" class="user-notifications"></div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // Simple notification function for testing
        function showNotification(message, type = 'info', duration = 5000) {
            const $container = $('#user-notifications');
            const id = 'notification-' + Date.now();
            const typeClass = 'user-notification-' + type;
            
            const $notification = $(`
                <div id="${id}" class="user-notification ${typeClass} user-fade-in">
                    <div class="notification-content">
                        <span class="notification-message">${message}</span>
                        <button class="notification-close" onclick="hideNotification('${id}')">&times;</button>
                    </div>
                </div>
            `);
            
            $container.append($notification);
            
            // Auto-hide after duration
            setTimeout(() => {
                hideNotification(id);
            }, duration);
        }
        
        function hideNotification(id) {
            const $notification = $('#' + id);
            $notification.fadeOut(300, function() {
                $(this).remove();
            });
        }
        
        // Test on page load
        $(document).ready(function() {
            console.log('Notification test page loaded');
            
            // Show a test notification after 1 second
            setTimeout(() => {
                showNotification('Test notification on page load - should have NO left border!', 'success');
            }, 1000);
        });
    </script>
</body>
</html>
