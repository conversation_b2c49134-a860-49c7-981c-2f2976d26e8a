<?php
/**
 * Bamboo CSRF and Styling Fixes Test
 * Company: Notepadsly
 * Version: 1.0
 * Description: Test CSRF token fixes and styling improvements
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/functions.php';

// Start session
session_start();

class CSRFAndStylingTest {
    private $test_user = [
        'username' => 'demohomexx',
        'password' => 'loving12',
        'user_id' => 9
    ];
    
    private $test_results = [];
    
    public function __construct() {
        echo "<h2>CSRF Token and Styling Fixes Test</h2>";
        echo "<p>Testing CSRF token resolution and styling improvements...</p>";
    }
    
    public function runTests() {
        $this->testUserLogin();
        $this->testCSRFTokenGeneration();
        $this->testRecordsPageFiles();
        $this->testDirectSubmissionFiles();
        $this->testStylingChanges();
        $this->testDynamicURLs();
        $this->testAppearanceSettings();
        $this->displayResults();
    }
    
    private function testUserLogin() {
        echo "<h3>1. User Login and Session Test</h3>";
        
        try {
            // Simulate login
            $user = fetchRow("SELECT * FROM users WHERE username = ?", [$this->test_user['username']]);
            
            if ($user && verifyPassword($this->test_user['password'], $user['password_hash'])) {
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['logged_in'] = true;
                
                $this->addResult('User Login', 'SUCCESS', "User logged in successfully");
                
            } else {
                $this->addResult('User Login', 'FAIL', 'Invalid credentials');
            }
            
        } catch (Exception $e) {
            $this->addResult('User Login', 'ERROR', $e->getMessage());
        }
    }
    
    private function testCSRFTokenGeneration() {
        echo "<h3>2. CSRF Token Generation Test</h3>";
        
        try {
            // Generate CSRF token
            $csrf_token = generateCSRFToken();
            $_SESSION['csrf_token'] = $csrf_token;
            
            if ($csrf_token && strlen($csrf_token) > 10) {
                $this->addResult('CSRF Token Generation', 'SUCCESS', 'Token: ' . substr($csrf_token, 0, 10) . '...');
                
                // Test token verification
                if (verifyCSRFToken($csrf_token)) {
                    $this->addResult('CSRF Token Verification', 'SUCCESS', 'Token verification works');
                } else {
                    $this->addResult('CSRF Token Verification', 'FAIL', 'Token verification failed');
                }
                
            } else {
                $this->addResult('CSRF Token Generation', 'FAIL', 'Invalid or empty token');
            }
            
        } catch (Exception $e) {
            $this->addResult('CSRF Token Generation', 'ERROR', $e->getMessage());
        }
    }
    
    private function testRecordsPageFiles() {
        echo "<h3>3. Records Page Files Test</h3>";
        
        $files_to_check = [
            'user/records/records.php' => 'Records main page',
            'user/records/records.css' => 'Records styling',
            'user/records/records.js' => 'Records JavaScript',
            'user/records/submit_task_direct.php' => 'Direct submission endpoint'
        ];
        
        foreach ($files_to_check as $file => $description) {
            if (file_exists(__DIR__ . '/../' . $file)) {
                $this->addResult($description, 'SUCCESS', "File exists: $file");
            } else {
                $this->addResult($description, 'FAIL', "File missing: $file");
            }
        }
    }
    
    private function testDirectSubmissionFiles() {
        echo "<h3>4. Direct Submission Files Test</h3>";
        
        $direct_files = [
            'user/records/submit_task_direct.php' => 'Records direct submission',
            'user/tasks/submit_task_direct.php' => 'Tasks direct submission'
        ];
        
        foreach ($direct_files as $file => $description) {
            if (file_exists(__DIR__ . '/../' . $file)) {
                $this->addResult($description, 'SUCCESS', "Direct submission file exists");
                
                // Test file content for key functions
                $content = file_get_contents(__DIR__ . '/../' . $file);
                if (strpos($content, 'verifyCSRFToken') !== false) {
                    $this->addResult($description . ' CSRF', 'SUCCESS', 'CSRF validation included');
                } else {
                    $this->addResult($description . ' CSRF', 'FAIL', 'CSRF validation missing');
                }
                
            } else {
                $this->addResult($description, 'FAIL', "File missing: $file");
            }
        }
    }
    
    private function testStylingChanges() {
        echo "<h3>5. Styling Changes Test</h3>";
        
        try {
            $css_file = __DIR__ . '/../user/records/records.css';
            if (file_exists($css_file)) {
                $css_content = file_get_contents($css_file);
                
                // Check for removal of left borders
                if (strpos($css_content, 'border-left: 4px solid') === false) {
                    $this->addResult('Left Border Removal', 'SUCCESS', 'Left borders removed from CSS');
                } else {
                    $this->addResult('Left Border Removal', 'FAIL', 'Left borders still present in CSS');
                }
                
                // Check for light background colors
                if (strpos($css_content, 'linear-gradient') !== false) {
                    $this->addResult('Light Background Colors', 'SUCCESS', 'Light gradient backgrounds added');
                } else {
                    $this->addResult('Light Background Colors', 'FAIL', 'Light backgrounds not found');
                }
                
            } else {
                $this->addResult('CSS File Check', 'FAIL', 'Records CSS file not found');
            }
            
        } catch (Exception $e) {
            $this->addResult('Styling Changes', 'ERROR', $e->getMessage());
        }
    }
    
    private function testDynamicURLs() {
        echo "<h3>6. Dynamic URL Configuration Test</h3>";
        
        try {
            // Test BASE_URL constant
            if (defined('BASE_URL')) {
                $this->addResult('BASE_URL Defined', 'SUCCESS', 'BASE_URL: ' . BASE_URL);
            } else {
                $this->addResult('BASE_URL Defined', 'FAIL', 'BASE_URL not defined');
            }
            
            // Test ASSETS_URL constant
            if (defined('ASSETS_URL')) {
                $this->addResult('ASSETS_URL Defined', 'SUCCESS', 'ASSETS_URL: ' . ASSETS_URL);
            } else {
                $this->addResult('ASSETS_URL Defined', 'FAIL', 'ASSETS_URL not defined');
            }
            
        } catch (Exception $e) {
            $this->addResult('Dynamic URLs', 'ERROR', $e->getMessage());
        }
    }
    
    private function testAppearanceSettings() {
        echo "<h3>7. Appearance Settings Test</h3>";
        
        try {
            // Test appearance settings function
            if (function_exists('getAppearanceSettings')) {
                $appearance_settings = getAppearanceSettings();
                $this->addResult('Appearance Settings Function', 'SUCCESS', 'Function exists and callable');
            } else {
                $this->addResult('Appearance Settings Function', 'FAIL', 'Function not found');
            }
            
        } catch (Exception $e) {
            $this->addResult('Appearance Settings', 'ERROR', $e->getMessage());
        }
    }
    
    private function addResult($test, $status, $message) {
        $this->test_results[] = [
            'test' => $test,
            'status' => $status,
            'message' => $message
        ];
        
        $color = $this->getStatusColor($status);
        echo "<p style='color: $color;'><strong>$test:</strong> $message</p>";
    }
    
    private function getStatusColor($status) {
        switch ($status) {
            case 'SUCCESS': return 'green';
            case 'FAIL': return 'red';
            case 'ERROR': return 'darkred';
            case 'WARNING': return 'orange';
            case 'INFO': return 'blue';
            default: return 'black';
        }
    }
    
    private function displayResults() {
        echo "<h3>Test Summary</h3>";
        
        $success_count = 0;
        $fail_count = 0;
        $error_count = 0;
        
        foreach ($this->test_results as $result) {
            switch ($result['status']) {
                case 'SUCCESS': $success_count++; break;
                case 'FAIL': $fail_count++; break;
                case 'ERROR': $error_count++; break;
            }
        }
        
        echo "<ul>";
        echo "<li style='color: green;'>Successful: $success_count</li>";
        echo "<li style='color: red;'>Failed: $fail_count</li>";
        echo "<li style='color: darkred;'>Errors: $error_count</li>";
        echo "</ul>";
        
        if ($fail_count > 0 || $error_count > 0) {
            echo "<h4>Issues Found:</h4>";
            echo "<ul>";
            foreach ($this->test_results as $result) {
                if ($result['status'] === 'FAIL' || $result['status'] === 'ERROR') {
                    $color = $this->getStatusColor($result['status']);
                    echo "<li style='color: $color;'>{$result['test']}: {$result['message']}</li>";
                }
            }
            echo "</ul>";
        }
        
        echo "<h3>Fixes Summary</h3>";
        echo "<ul>";
        echo "<li>✅ <strong>Styling Fixed:</strong> Removed left borders, added light gradient backgrounds</li>";
        echo "<li>✅ <strong>CSRF Token Issues:</strong> Enhanced token generation and validation</li>";
        echo "<li>✅ <strong>Direct Connection:</strong> Created fallback direct database submission</li>";
        echo "<li>✅ <strong>Dynamic URLs:</strong> Proper BASE_URL configuration for all environments</li>";
        echo "<li>✅ <strong>Error Handling:</strong> Comprehensive error reporting and recovery</li>";
        echo "<li>✅ <strong>Appearance Settings:</strong> Proper header variable initialization</li>";
        echo "</ul>";
        
        echo "<p><strong>Test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
    }
}

// Run the test
echo "<!DOCTYPE html><html><head><title>CSRF and Styling Fixes Test</title></head><body>";

$test = new CSRFAndStylingTest();
$test->runTests();

echo "</body></html>";
?>
