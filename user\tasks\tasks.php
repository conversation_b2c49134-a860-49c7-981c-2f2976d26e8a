<?php
/**
 * Bamboo User Dashboard - Task Submission Page (Recreated)
 * Company: Notepadsly
 * Version: 2.0
 * Description: Robust task submission system with proper error handling
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define app constant
if (!defined('BAMBOO_APP')) {
    define('BAMBOO_APP', true);
}

try {
    // Include required files with error handling
    require_once '../../includes/config.php';
    require_once '../../includes/functions.php';

    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in
    if (!isLoggedIn()) {
        redirect('user/login/');
        exit;
    }

    // Get current user information with error handling
    $current_user = getCurrentUser();
    if (!$current_user) {
        throw new Exception('Failed to get user information');
    }
    $user_id = $current_user['id'];

    // Initialize default values
    $user_balance = ['balance' => 0, 'commission_balance' => 0, 'frozen_balance' => 0];
    $user_vip = ['level' => 1, 'name' => 'Bronze', 'max_daily_tasks' => 5];
    $tasks_completed_today = 0;
    $today_profit = 0;
    $min_balance_required = 100;
    $active_task = null;
    $available_products = [];

    // Get user financial data with error handling
    try {
        $balance_result = getUserBalance($user_id);
        if ($balance_result) {
            $user_balance = $balance_result;
        }
    } catch (Exception $e) {
        logError("Failed to get user balance for user $user_id: " . $e->getMessage());
    }

    // Get user VIP level with error handling
    try {
        $vip_result = getUserVipLevel($user_id);
        if ($vip_result) {
            $user_vip = $vip_result;
        } else {
            // Fallback: get VIP level directly from user table
            $user_vip_level = $current_user['vip_level'] ?? 1;
            $vip_fallback = fetchRow("SELECT * FROM vip_levels WHERE level = ?", [$user_vip_level]);
            if ($vip_fallback) {
                $user_vip = $vip_fallback;
            }
        }
    } catch (Exception $e) {
        logError("Failed to get user VIP level for user $user_id: " . $e->getMessage());
    }

    // Get today's task progress with error handling
    try {
        $tasks_completed_today = getTasksCompletedToday($user_id);
    } catch (Exception $e) {
        logError("Failed to get tasks completed today for user $user_id: " . $e->getMessage());
        // Try alternative query
        try {
            $result = fetchRow("SELECT COUNT(*) as count FROM tasks WHERE user_id = ? AND status = 'completed' AND DATE(created_at) = CURDATE()", [$user_id]);
            $tasks_completed_today = $result['count'] ?? 0;
        } catch (Exception $e2) {
            logError("Alternative task count query also failed: " . $e2->getMessage());
        }
    }

    // Set daily task limit
    $daily_task_limit = $user_vip['max_daily_tasks'] ?? $user_vip['daily_task_limit'] ?? 5;

    // Calculate today's profit with error handling
    try {
        $today_profit_sql = "SELECT SUM(commission_earned) as today_profit
                             FROM tasks
                             WHERE user_id = ? AND status = 'completed' AND DATE(completed_at) = CURDATE()";
        $today_profit_result = fetchRow($today_profit_sql, [$user_id]);
        $today_profit = $today_profit_result['today_profit'] ?? 0;
    } catch (Exception $e) {
        logError("Failed to calculate today's profit for user $user_id: " . $e->getMessage());
    }

    // Get minimum balance requirement with error handling
    try {
        $min_balance_required = getAppSetting('min_wallet_balance_for_orders', 100);
    } catch (Exception $e) {
        logError("Failed to get app setting: " . $e->getMessage());
        // Try alternative settings query
        try {
            $setting_result = fetchRow("SELECT value FROM settings WHERE `key` = 'min_wallet_balance_for_orders' OR setting_key = 'min_wallet_balance_for_orders'");
            $min_balance_required = $setting_result['value'] ?? 100;
        } catch (Exception $e2) {
            $min_balance_required = 100; // Default fallback
        }
    }

    // Check for active task with error handling
    try {
        $active_task_sql = "SELECT t.*, p.name as product_name, p.image_url, p.price
                            FROM tasks t
                            LEFT JOIN products p ON t.product_id = p.id
                            WHERE t.user_id = ? AND t.status IN ('assigned', 'in_progress')
                            ORDER BY t.created_at DESC LIMIT 1";
        $active_task = fetchRow($active_task_sql, [$user_id]);
    } catch (Exception $e) {
        logError("Failed to get active task for user $user_id: " . $e->getMessage());
    }

    // Get available products for display with error handling
    try {
        $products_sql = "SELECT * FROM products
                         WHERE status = 'active' AND (min_vip_level <= ? OR min_vip_level IS NULL)
                         ORDER BY RAND() LIMIT 9";
        $available_products = fetchAll($products_sql, [$user_vip['level'] ?? 1]);

        // If no products found, get any active products
        if (empty($available_products)) {
            $available_products = fetchAll("SELECT * FROM products WHERE status = 'active' LIMIT 9");
        }
    } catch (Exception $e) {
        logError("Failed to get available products: " . $e->getMessage());
    }

    // Page configuration
    $page_title = 'Task Submission';
    $page_description = 'Product optimization and task submission system';
    $page_css = 'tasks.css';
    $page_js = 'tasks.js';

} catch (Exception $e) {
    // Critical error - log and show error page
    logError("Critical error in tasks page: " . $e->getMessage());

    // Show error page
    echo "<!DOCTYPE html><html><head><title>Error</title></head><body>";
    echo "<h1>System Error</h1>";
    echo "<p>We're experiencing technical difficulties. Please try again later.</p>";
    echo "<p><a href='../dashboard/'>Return to Dashboard</a></p>";
    echo "</body></html>";
    exit;
}

// Include header
include '../includes/user_header.php';
?>

<div class="user-container">
    <!-- Task Page Title -->
    <div class="task-page-title user-fade-in">
        <div class="user-container">
            <h1 class="page-title">Product Matching Tasks</h1>
            <p class="page-subtitle">Complete product optimization tasks to earn commissions</p>
        </div>
    </div>

    <!-- Task Statistics -->
    <div class="task-stats-container user-slide-in">
        <div class="user-row">
            <div class="user-col-6">
                <div class="stat-card profit-card">
                    <div class="stat-label">Today Profit</div>
                    <div class="stat-value" id="todayProfit">USDT <?php echo number_format($today_profit, 2); ?></div>
                    <div class="stat-note">Daily Earnings are automatically Updated</div>
                </div>
            </div>
            <div class="user-col-6">
                <div class="stat-card balance-card">
                    <div class="stat-label">Today Balance</div>
                    <div class="stat-value" id="todayBalance">USDT <?php echo number_format($user_balance['balance'] ?? 0, 2); ?></div>
                    <div class="stat-note">Each profit earned will be shown and added to the total</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Task Progress -->
    <div class="task-progress-container user-fade-in">
        <div class="progress-info">
            <span class="progress-label">Task Progress:</span>
            <span class="progress-count" id="taskProgress"><?php echo $tasks_completed_today; ?>/<?php echo $daily_task_limit; ?></span>
        </div>
        <div class="progress-bar-container">
            <div class="progress-bar" style="width: <?php echo ($tasks_completed_today / $daily_task_limit) * 100; ?>%"></div>
        </div>
    </div>

    <!-- Product Grid or Active Task -->
    <div class="task-content">
        <?php if ($active_task): ?>
            <!-- Active Task Display -->
            <div id="activeTaskContainer" class="active-task-container user-fade-in">
                <div class="active-task-card">
                    <div class="task-product-image">
                        <?php if ($active_task['image_url']): ?>
                            <img src="<?php echo htmlspecialchars($active_task['image_url']); ?>" alt="<?php echo htmlspecialchars($active_task['product_name']); ?>">
                        <?php else: ?>
                            <div class="product-placeholder">
                                <i class="icon-product"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="task-details">
                        <h3 class="product-name"><?php echo htmlspecialchars($active_task['product_name']); ?></h3>
                        
                        <div class="task-info-grid">
                            <div class="info-item">
                                <span class="info-label">Price:</span>
                                <span class="info-value">USDT <?php echo number_format($active_task['amount'] ?? 0, 2); ?></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Total Amount:</span>
                                <span class="info-value">USDT <?php echo number_format($active_task['amount'] ?? 0, 2); ?></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Total Profit:</span>
                                <span class="info-value profit-highlight">USDT <?php echo number_format($active_task['commission_earned'] ?? 0, 2); ?></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Appraisals No:</span>
                                <span class="info-value"><?php echo htmlspecialchars($active_task['appraisal_no'] ?? 'N/A'); ?></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Creation Time:</span>
                                <span class="info-value"><?php echo date('Y-m-d H:i:s', strtotime($active_task['assigned_at'])); ?></span>
                            </div>
                        </div>
                        
                        <?php if ($user_balance['balance'] < 0 && $active_task['is_negative_trigger']): ?>
                            <!-- Negative Balance Warning -->
                            <div class="alert alert-warning mb-3">
                                <h6><i class="bi bi-exclamation-triangle me-2"></i>Deposit Required</h6>
                                <p class="mb-2">Your current balance: <strong>USDT <?php echo number_format($user_balance['balance'] ?? 0, 2); ?></strong></p>
                                <p class="mb-0">You need to deposit funds to submit this task.</p>
                            </div>
                        <?php endif; ?>

                        <div class="task-actions">
                            <?php if ($user_balance['balance'] < 0 && $active_task['is_negative_trigger']): ?>
                                <button class="user-btn user-btn-warning user-btn-lg"
                                        onclick="window.location.href='../deposit/deposit.php'">
                                    <i class="bi bi-plus-circle"></i>
                                    Deposit Now
                                </button>
                                <button id="submitTaskBtn" class="user-btn user-btn-primary user-btn-lg"
                                        data-task-id="<?php echo $active_task['id']; ?>" disabled>
                                    <i class="icon-submit"></i>
                                    Submit (Deposit Required)
                                </button>
                            <?php else: ?>
                                <button id="submitTaskBtn" class="user-btn user-btn-primary user-btn-lg"
                                        data-task-id="<?php echo $active_task['id']; ?>">
                                    <i class="icon-submit"></i>
                                    Submit
                                </button>
                            <?php endif; ?>
                            <button id="cancelTaskBtn" class="user-btn user-btn-secondary user-btn-lg"
                                    data-task-id="<?php echo $active_task['id']; ?>">
                                <i class="icon-close"></i>
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- Product Grid -->
            <div id="productGrid" class="product-grid user-fade-in">
                <h3 class="grid-title">Available Products</h3>
                <div class="products-container">
                    <?php foreach ($available_products as $index => $product): ?>
                        <div class="product-item" data-product-id="<?php echo $product['id']; ?>">
                            <div class="product-image">
                                <?php if ($product['image_url']): ?>
                                    <img src="<?php echo htmlspecialchars($product['image_url']); ?>" alt="<?php echo htmlspecialchars($product['name']); ?>">
                                <?php else: ?>
                                    <div class="product-placeholder">
                                        <i class="icon-product"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="product-overlay">
                                <span class="product-name"><?php echo htmlspecialchars($product['name']); ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Start Matching Button -->
            <div class="matching-controls user-fade-in">
                <button id="startMatchingBtn" class="user-btn user-btn-primary user-btn-lg start-matching-btn"
                        <?php if ($user_balance['balance'] < $min_balance_required): ?>disabled title="Insufficient balance"<?php endif; ?>
                        <?php if ($tasks_completed_today >= $daily_task_limit): ?>disabled title="Daily task limit reached"<?php endif; ?>>
                    <i class="icon-play"></i>
                    Start Matching
                </button>
                
                <?php if ($user_balance['balance'] < $min_balance_required): ?>
                    <div class="warning-message">
                        <i class="icon-warning"></i>
                        Insufficient balance. Minimum required: USDT <?php echo number_format($min_balance_required, 2); ?>
                        <a href="<?php echo BASE_URL; ?>user/deposit/" class="user-btn user-btn-sm user-btn-outline">Deposit Now</a>
                    </div>
                <?php endif; ?>
                
                <?php if ($tasks_completed_today >= $daily_task_limit): ?>
                    <div class="info-message">
                        <i class="icon-info"></i>
                        Daily task limit reached. Come back tomorrow or upgrade your VIP level.
                        <a href="<?php echo BASE_URL; ?>user/vip/" class="user-btn user-btn-sm user-btn-outline">View VIP Levels</a>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>


</div>

<!-- Simple Modal for Confirmations -->
<div id="confirmModal" class="simple-modal" style="display: none;">
    <div class="modal-overlay"></div>
    <div class="modal-content">
        <div class="modal-header">
            <h4 id="modalTitle">Confirm Action</h4>
        </div>
        <div class="modal-body">
            <p id="modalMessage">Are you sure?</p>
        </div>
        <div class="modal-footer">
            <button id="modalCancel" class="user-btn user-btn-secondary">Cancel</button>
            <button id="modalConfirm" class="user-btn user-btn-primary">Confirm</button>
        </div>
    </div>
</div>

<!-- Hidden data for JavaScript -->
<script>
    window.TaskData = {
        userId: <?php echo $user_id; ?>,
        currentBalance: <?php echo $user_balance['balance'] ?? 0; ?>,
        todayProfit: <?php echo $today_profit; ?>,
        tasksCompleted: <?php echo $tasks_completed_today; ?>,
        dailyLimit: <?php echo $daily_task_limit; ?>,
        minBalance: <?php echo $min_balance_required; ?>,
        hasActiveTask: <?php echo $active_task ? 'true' : 'false'; ?>,
        activeTaskId: <?php echo $active_task['id'] ?? 'null'; ?>
    };
</script>

<?php
// Include footer
include '../includes/user_footer.php';
?>
