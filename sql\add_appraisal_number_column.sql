-- Add appraisal_number column to tasks table
-- This migration adds the appraisal_number field for auto-generated task appraisal numbers

ALTER TABLE `tasks` 
ADD COLUMN `appraisal_number` VARCHAR(20) NULL AFTER `commission_earned`,
ADD INDEX `idx_appraisal_number` (`appraisal_number`);

-- Update existing tasks with generated appraisal numbers
UPDATE `tasks` 
SET `appraisal_number` = CONCAT('APR', DATE_FORMAT(assigned_at, '%y%m%d'), LPAD(id, 4, '0'))
WHERE `appraisal_number` IS NULL;
