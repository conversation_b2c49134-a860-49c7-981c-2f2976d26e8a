<?php
/**
 * Bamboo Direct Task Submission
 * Company: Notepadsly
 * Version: 1.0
 * Description: Direct database task submission to bypass API issues
 */

// Define app constant
if (!defined('BAMBOO_APP')) {
    define('BAMBOO_APP', true);
}

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set JSON header
header('Content-Type: application/json');

try {
    // Check if user is logged in
    if (!isLoggedIn()) {
        throw new Exception('User not logged in');
    }

    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('POST method required');
    }

    // Get current user
    $current_user = getCurrentUser();
    if (!$current_user) {
        throw new Exception('Failed to get user information');
    }
    $user_id = $current_user['id'];

    // Validate CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        logError("Invalid CSRF token for user $user_id in direct task submission");
        throw new Exception('Invalid CSRF token');
    }

    // Get task ID
    $task_id = intval($_POST['task_id'] ?? 0);
    if ($task_id <= 0) {
        throw new Exception('Invalid task ID');
    }

    // Get task details
    $task_sql = "SELECT t.*, p.name as product_name, p.price 
                 FROM tasks t 
                 LEFT JOIN products p ON t.product_id = p.id 
                 WHERE t.id = ? AND t.user_id = ? AND t.status IN ('assigned', 'in_progress')";
    $task = fetchRow($task_sql, [$task_id, $user_id]);

    if (!$task) {
        throw new Exception('Task not found or already completed');
    }

    // Start database transaction
    $db = getDB();
    $db->beginTransaction();

    try {
        // Update task status
        $update_task_sql = "UPDATE tasks SET status = 'completed', completed_at = NOW(), updated_at = NOW() WHERE id = ?";
        $task_update_result = executeQuery($update_task_sql, [$task_id]);

        if (!$task_update_result) {
            throw new Exception('Failed to update task status');
        }

        // Calculate returns
        $total_return = $task['amount'] + $task['commission_earned'];

        // Update user balance
        $update_balance_sql = "UPDATE users SET
                               balance = balance + ?,
                               commission_balance = commission_balance + ?,
                               frozen_balance = frozen_balance - ?,
                               total_commission_earned = total_commission_earned + ?,
                               tasks_completed_today = tasks_completed_today + 1,
                               last_task_date = CURDATE()
                               WHERE id = ?";
        $balance_update_result = executeQuery($update_balance_sql, [
            $total_return,
            $task['commission_earned'],
            $task['amount'],
            $task['commission_earned'],
            $user_id
        ]);

        if (!$balance_update_result) {
            throw new Exception('Failed to update user balance');
        }

        // Record transactions
        recordTransaction($user_id, $task['amount'], 'task_return', 'Task amount returned for: ' . $task['product_name'], 'completed');
        recordTransaction($user_id, $task['commission_earned'], 'commission', 'Commission earned from task: ' . $task['product_name'], 'completed');

        // Commit transaction
        $db->commit();

        // Get updated data
        $updated_balance = getUserBalance($user_id);
        $updated_tasks_today = getTasksCompletedToday($user_id);
        $user_vip = getUserVipLevel($user_id);
        $today_profit_result = fetchRow("SELECT SUM(commission_earned) as today_profit FROM tasks WHERE user_id = ? AND DATE(completed_at) = CURDATE() AND status = 'completed'", [$user_id]);
        $today_profit = $today_profit_result['today_profit'] ?? 0;

        // Check workflow status
        $daily_limit_reached = $updated_tasks_today >= ($user_vip['max_daily_tasks'] ?? 5);
        $next_task_number = $updated_tasks_today + 1;
        $has_negative_trigger = false;
        
        if (!$daily_limit_reached) {
            $negative_setting = fetchRow("SELECT * FROM negative_settings WHERE user_id = ? AND trigger_task_number = ? AND is_active = 1 AND is_triggered = 0", [$user_id, $next_task_number]);
            $has_negative_trigger = !empty($negative_setting);
        }

        $workflow_status = $daily_limit_reached ? 'daily_complete' : ($has_negative_trigger ? 'negative_trigger_pending' : 'can_continue');

        // Success response
        $response = [
            'success' => true,
            'message' => 'Task submitted successfully via direct connection',
            'data' => [
                'profit_earned' => number_format($task['commission_earned'], 2),
                'new_balance' => number_format($updated_balance['balance'], 2),
                'today_profit' => number_format($today_profit, 2),
                'tasks_completed' => $updated_tasks_today,
                'daily_limit_reached' => $daily_limit_reached,
                'max_daily_tasks' => $user_vip['max_daily_tasks'] ?? 5,
                'can_continue_matching' => !$daily_limit_reached,
                'has_negative_trigger_next' => $has_negative_trigger,
                'workflow_status' => $workflow_status,
                'submission_method' => 'direct_database'
            ]
        ];

        echo json_encode($response);

    } catch (Exception $e) {
        // Rollback transaction
        $db->rollback();
        throw $e;
    }

} catch (Exception $e) {
    // Log error
    logError("Direct task submission error: " . $e->getMessage());
    
    // Error response
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'error_code' => 'DIRECT_SUBMISSION_ERROR',
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    http_response_code(400);
    echo json_encode($response);
}
?>
