<?php
/**
 * Debug Records Page
 * Company: Notepadsly
 * Version: 1.0
 * Description: Debug version to check what's happening with records page
 */

// Define app constant
if (!defined('BAMBOO_APP')) {
    define('BAMBOO_APP', true);
}

try {
    // Include required files
    require_once '../../includes/config.php';
    require_once '../../includes/functions.php';

    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in
    if (!isLoggedIn()) {
        redirect('user/login/');
        exit;
    }

    // Get current user information
    $current_user = getCurrentUser();
    if (!$current_user) {
        throw new Exception('Failed to get user information');
    }
    $user_id = $current_user['id'];

    echo "<h1>Records Page Debug</h1>";
    echo "<p>User ID: $user_id</p>";

    // Test user balance
    try {
        $user_balance = getUserBalance($user_id);
        echo "<h3>User Balance:</h3>";
        echo "<pre>" . print_r($user_balance, true) . "</pre>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>Balance Error: " . $e->getMessage() . "</p>";
    }

    // Test user VIP
    try {
        $user_vip = getUserVipLevel($user_id);
        echo "<h3>User VIP:</h3>";
        echo "<pre>" . print_r($user_vip, true) . "</pre>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>VIP Error: " . $e->getMessage() . "</p>";
    }

    // Test pending tasks
    try {
        $pending_tasks_sql = "SELECT t.*, 
                                     p.name as product_name, 
                                     p.image_url, 
                                     p.price, 
                                     p.description,
                                     p.category,
                                     p.brand,
                                     p.rating,
                                     p.reviews_count,
                                     p.discount_percentage,
                                     p.original_price
                              FROM tasks t
                              LEFT JOIN products p ON t.product_id = p.id
                              WHERE t.user_id = ? AND t.status IN ('assigned', 'in_progress')
                              ORDER BY t.assigned_at ASC";
        $pending_tasks = fetchAll($pending_tasks_sql, [$user_id]);
        
        echo "<h3>Pending Tasks Query Result:</h3>";
        echo "<p>Query: " . htmlspecialchars($pending_tasks_sql) . "</p>";
        echo "<p>User ID: $user_id</p>";
        echo "<p>Result type: " . gettype($pending_tasks) . "</p>";
        
        if (is_array($pending_tasks)) {
            echo "<p>Count: " . count($pending_tasks) . "</p>";
            echo "<pre>" . print_r($pending_tasks, true) . "</pre>";
        } else {
            echo "<p style='color: red;'>Result is not an array: " . var_export($pending_tasks, true) . "</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Pending Tasks Error: " . $e->getMessage() . "</p>";
    }

    // Test completed tasks
    try {
        $completed_tasks_today = getTasksCompletedToday($user_id);
        echo "<h3>Completed Tasks Today:</h3>";
        echo "<p>Count: $completed_tasks_today</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>Completed Tasks Today Error: " . $e->getMessage() . "</p>";
    }

    // Test completed tasks with pagination
    try {
        $page = 1;
        $per_page = 10;
        $offset = ($page - 1) * $per_page;
        
        // Get total count for pagination
        $count_sql = "SELECT COUNT(*) as total FROM tasks WHERE user_id = ? AND status = 'completed'";
        $count_result = fetchRow($count_sql, [$user_id]);
        $total_completed = $count_result['total'] ?? 0;
        $total_pages = ceil($total_completed / $per_page);
        
        echo "<h3>Completed Tasks Pagination:</h3>";
        echo "<p>Total completed: $total_completed</p>";
        echo "<p>Total pages: $total_pages</p>";
        
        // Get completed tasks for current page
        $recent_tasks_sql = "SELECT t.*, p.name as product_name, p.image_url, p.price, p.brand, p.category
                             FROM tasks t
                             LEFT JOIN products p ON t.product_id = p.id
                             WHERE t.user_id = ? AND t.status = 'completed'
                             ORDER BY t.completed_at DESC 
                             LIMIT ? OFFSET ?";
        $recent_completed_tasks = fetchAll($recent_tasks_sql, [$user_id, $per_page, $offset]);
        
        echo "<p>Recent completed tasks type: " . gettype($recent_completed_tasks) . "</p>";
        if (is_array($recent_completed_tasks)) {
            echo "<p>Recent completed tasks count: " . count($recent_completed_tasks) . "</p>";
            echo "<pre>" . print_r($recent_completed_tasks, true) . "</pre>";
        } else {
            echo "<p style='color: red;'>Recent completed tasks is not an array: " . var_export($recent_completed_tasks, true) . "</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Completed Tasks Pagination Error: " . $e->getMessage() . "</p>";
    }

    // Test appearance settings
    try {
        $appearance_settings = getAppearanceSettings();
        echo "<h3>Appearance Settings:</h3>";
        echo "<pre>" . print_r($appearance_settings, true) . "</pre>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>Appearance Settings Error: " . $e->getMessage() . "</p>";
    }

} catch (Exception $e) {
    echo "<h1>Critical Error</h1>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
