<?php
/**
 * Bamboo Task Operations - Comprehensive Test Framework
 * Company: Notepadsly
 * Version: 1.0
 * Description: Test framework for validating task operations and error handling
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/functions.php';

// Start session
session_start();

// Test configuration
$test_config = [
    'test_user_id' => 1, // Use existing test user
    'test_username' => 'demohomexx',
    'test_password' => 'loving12',
    'verbose' => true
];

class TaskOperationsTest {
    private $config;
    private $test_results = [];
    private $user_id;
    
    public function __construct($config) {
        $this->config = $config;
        $this->log("=== Bamboo Task Operations Test Framework ===");
        $this->log("Test started at: " . date('Y-m-d H:i:s'));
    }
    
    public function runAllTests() {
        $this->log("\n=== Starting Comprehensive Test Suite ===");
        
        // Test 1: Database Connection
        $this->testDatabaseConnection();
        
        // Test 2: User Authentication
        $this->testUserAuthentication();
        
        // Test 3: API Endpoint Accessibility
        $this->testAPIEndpoints();
        
        // Test 4: Task Creation Logic
        $this->testTaskCreation();
        
        // Test 5: Task Submission Logic
        $this->testTaskSubmission();
        
        // Test 6: Task Cancellation Logic
        $this->testTaskCancellation();
        
        // Test 7: Error Handling
        $this->testErrorHandling();
        
        // Test 8: Database Schema Validation
        $this->testDatabaseSchema();
        
        $this->generateReport();
    }
    
    private function testDatabaseConnection() {
        $this->log("\n--- Test 1: Database Connection ---");
        
        try {
            $db = getDB();
            $stmt = $db->query("SELECT 1");
            if ($stmt) {
                $this->addResult('Database Connection', 'PASS', 'Successfully connected to database');
            } else {
                $this->addResult('Database Connection', 'FAIL', 'Failed to execute test query');
            }
        } catch (Exception $e) {
            $this->addResult('Database Connection', 'FAIL', 'Exception: ' . $e->getMessage());
        }
    }
    
    private function testUserAuthentication() {
        $this->log("\n--- Test 2: User Authentication ---");
        
        try {
            // Test user exists
            $user = fetchRow("SELECT * FROM users WHERE username = ?", [$this->config['test_username']]);
            if ($user) {
                $this->user_id = $user['id'];
                $this->addResult('User Exists', 'PASS', "User {$this->config['test_username']} found with ID: {$this->user_id}");
                
                // Test password verification
                if (verifyPassword($this->config['test_password'], $user['password_hash'])) {
                    $this->addResult('Password Verification', 'PASS', 'Password verification successful');
                } else {
                    $this->addResult('Password Verification', 'FAIL', 'Password verification failed');
                }
                
                // Test user balance retrieval
                $balance = getUserBalance($this->user_id);
                if ($balance) {
                    $this->addResult('Balance Retrieval', 'PASS', "Balance: {$balance['balance']}, Commission: {$balance['commission_balance']}");
                } else {
                    $this->addResult('Balance Retrieval', 'FAIL', 'Failed to retrieve user balance');
                }
                
            } else {
                $this->addResult('User Exists', 'FAIL', "User {$this->config['test_username']} not found");
            }
        } catch (Exception $e) {
            $this->addResult('User Authentication', 'FAIL', 'Exception: ' . $e->getMessage());
        }
    }
    
    private function testAPIEndpoints() {
        $this->log("\n--- Test 3: API Endpoint Accessibility ---");
        
        $endpoints = [
            'tasks.php' => '../user/api/tasks.php',
            'balance.php' => '../user/api/balance.php',
            'transactions.php' => '../user/api/transactions.php'
        ];
        
        foreach ($endpoints as $name => $path) {
            if (file_exists(__DIR__ . '/' . $path)) {
                $this->addResult("API Endpoint: $name", 'PASS', "File exists at $path");
            } else {
                $this->addResult("API Endpoint: $name", 'FAIL', "File not found at $path");
            }
        }
    }
    
    private function testTaskCreation() {
        $this->log("\n--- Test 4: Task Creation Logic ---");
        
        if (!$this->user_id) {
            $this->addResult('Task Creation', 'SKIP', 'No valid user ID for testing');
            return;
        }
        
        try {
            // Test VIP level retrieval
            $vip_level = getUserVipLevel($this->user_id);
            if ($vip_level) {
                $this->addResult('VIP Level Retrieval', 'PASS', "VIP Level: {$vip_level['level']}, Name: {$vip_level['name']}");
            } else {
                $this->addResult('VIP Level Retrieval', 'FAIL', 'Failed to retrieve VIP level');
            }
            
            // Test product availability
            $products = fetchAll("SELECT * FROM products WHERE status = 'active' LIMIT 5");
            if ($products && count($products) > 0) {
                $this->addResult('Product Availability', 'PASS', count($products) . ' active products found');
            } else {
                $this->addResult('Product Availability', 'FAIL', 'No active products found');
            }
            
            // Test negative settings check
            $negative_settings = fetchAll("SELECT * FROM negative_settings WHERE user_id = ? AND is_active = 1", [$this->user_id]);
            $this->addResult('Negative Settings Check', 'INFO', count($negative_settings) . ' active negative settings found');
            
        } catch (Exception $e) {
            $this->addResult('Task Creation Logic', 'FAIL', 'Exception: ' . $e->getMessage());
        }
    }
    
    private function testTaskSubmission() {
        $this->log("\n--- Test 5: Task Submission Logic ---");
        
        if (!$this->user_id) {
            $this->addResult('Task Submission', 'SKIP', 'No valid user ID for testing');
            return;
        }
        
        try {
            // Check for existing active tasks
            $active_tasks = fetchAll("SELECT * FROM tasks WHERE user_id = ? AND status IN ('assigned', 'in_progress')", [$this->user_id]);
            $this->addResult('Active Tasks Check', 'INFO', count($active_tasks) . ' active tasks found');
            
            // Test task completion logic (without actually completing)
            $completed_today = getTasksCompletedToday($this->user_id);
            $this->addResult('Tasks Completed Today', 'INFO', "$completed_today tasks completed today");
            
            // Test commission calculation
            $test_amount = 100;
            $test_rate = 5;
            $expected_commission = ($test_amount * $test_rate) / 100;
            $this->addResult('Commission Calculation', 'PASS', "Test: $test_amount * $test_rate% = $expected_commission");
            
        } catch (Exception $e) {
            $this->addResult('Task Submission Logic', 'FAIL', 'Exception: ' . $e->getMessage());
        }
    }
    
    private function testTaskCancellation() {
        $this->log("\n--- Test 6: Task Cancellation Logic ---");
        
        try {
            // Test task status validation
            $valid_statuses = ['assigned', 'in_progress', 'completed', 'failed', 'cancelled'];
            $this->addResult('Task Status Validation', 'PASS', 'Valid statuses: ' . implode(', ', $valid_statuses));
            
        } catch (Exception $e) {
            $this->addResult('Task Cancellation Logic', 'FAIL', 'Exception: ' . $e->getMessage());
        }
    }
    
    private function testErrorHandling() {
        $this->log("\n--- Test 7: Error Handling ---");
        
        try {
            // Test CSRF token generation
            $csrf_token = generateCSRFToken();
            if ($csrf_token && strlen($csrf_token) > 10) {
                $this->addResult('CSRF Token Generation', 'PASS', 'Token generated successfully');
            } else {
                $this->addResult('CSRF Token Generation', 'FAIL', 'Invalid or empty token');
            }
            
            // Test error logging
            $test_message = "Test error message - " . date('Y-m-d H:i:s');
            logError($test_message);
            $this->addResult('Error Logging', 'PASS', 'Error logged successfully');
            
        } catch (Exception $e) {
            $this->addResult('Error Handling', 'FAIL', 'Exception: ' . $e->getMessage());
        }
    }
    
    private function testDatabaseSchema() {
        $this->log("\n--- Test 8: Database Schema Validation ---");
        
        try {
            $required_tables = ['users', 'tasks', 'products', 'transactions', 'vip_levels', 'negative_settings'];
            
            foreach ($required_tables as $table) {
                if (tableExists($table)) {
                    $count = getRecordCount($table);
                    $this->addResult("Table: $table", 'PASS', "$count records found");
                } else {
                    $this->addResult("Table: $table", 'FAIL', 'Table does not exist');
                }
            }
            
            // Test specific columns
            $task_columns = $this->getTableColumns('tasks');
            $required_task_columns = ['id', 'user_id', 'product_id', 'amount', 'commission_earned', 'status'];
            
            foreach ($required_task_columns as $column) {
                if (in_array($column, $task_columns)) {
                    $this->addResult("Tasks Column: $column", 'PASS', 'Column exists');
                } else {
                    $this->addResult("Tasks Column: $column", 'FAIL', 'Column missing');
                }
            }
            
        } catch (Exception $e) {
            $this->addResult('Database Schema', 'FAIL', 'Exception: ' . $e->getMessage());
        }
    }
    
    private function getTableColumns($table) {
        try {
            $db = getDB();
            $stmt = $db->query("DESCRIBE $table");
            $columns = [];
            while ($row = $stmt->fetch()) {
                $columns[] = $row['Field'];
            }
            return $columns;
        } catch (Exception $e) {
            return [];
        }
    }
    
    private function addResult($test_name, $status, $message) {
        $this->test_results[] = [
            'test' => $test_name,
            'status' => $status,
            'message' => $message,
            'timestamp' => date('H:i:s')
        ];
        
        if ($this->config['verbose']) {
            $status_symbol = $this->getStatusSymbol($status);
            $this->log("  $status_symbol $test_name: $message");
        }
    }
    
    private function getStatusSymbol($status) {
        switch ($status) {
            case 'PASS': return '✓';
            case 'FAIL': return '✗';
            case 'SKIP': return '⊝';
            case 'INFO': return 'ℹ';
            default: return '?';
        }
    }
    
    private function generateReport() {
        $this->log("\n=== Test Results Summary ===");
        
        $pass_count = 0;
        $fail_count = 0;
        $skip_count = 0;
        $info_count = 0;
        
        foreach ($this->test_results as $result) {
            switch ($result['status']) {
                case 'PASS': $pass_count++; break;
                case 'FAIL': $fail_count++; break;
                case 'SKIP': $skip_count++; break;
                case 'INFO': $info_count++; break;
            }
        }
        
        $total_tests = count($this->test_results);
        $this->log("Total Tests: $total_tests");
        $this->log("✓ Passed: $pass_count");
        $this->log("✗ Failed: $fail_count");
        $this->log("⊝ Skipped: $skip_count");
        $this->log("ℹ Info: $info_count");
        
        if ($fail_count > 0) {
            $this->log("\n=== Failed Tests ===");
            foreach ($this->test_results as $result) {
                if ($result['status'] === 'FAIL') {
                    $this->log("✗ {$result['test']}: {$result['message']}");
                }
            }
        }
        
        $this->log("\nTest completed at: " . date('Y-m-d H:i:s'));
        
        // Save results to file
        $this->saveResultsToFile();
    }
    
    private function saveResultsToFile() {
        $filename = __DIR__ . '/task_test_results_' . date('Y-m-d_H-i-s') . '.json';
        $report_data = [
            'timestamp' => date('Y-m-d H:i:s'),
            'config' => $this->config,
            'results' => $this->test_results,
            'summary' => [
                'total' => count($this->test_results),
                'passed' => count(array_filter($this->test_results, fn($r) => $r['status'] === 'PASS')),
                'failed' => count(array_filter($this->test_results, fn($r) => $r['status'] === 'FAIL')),
                'skipped' => count(array_filter($this->test_results, fn($r) => $r['status'] === 'SKIP')),
                'info' => count(array_filter($this->test_results, fn($r) => $r['status'] === 'INFO'))
            ]
        ];
        
        file_put_contents($filename, json_encode($report_data, JSON_PRETTY_PRINT));
        $this->log("Results saved to: $filename");
    }
    
    private function log($message) {
        echo $message . "\n";
        if (php_sapi_name() !== 'cli') {
            echo "<br>";
        }
    }
}

// Run tests if accessed directly
if (basename($_SERVER['PHP_SELF']) === 'task_operations_test.php') {
    $test = new TaskOperationsTest($test_config);
    $test->runAllTests();
}
?>
