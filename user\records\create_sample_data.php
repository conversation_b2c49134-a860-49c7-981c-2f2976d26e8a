<?php
/**
 * Create Sample Data for Records Page Testing
 * Company: Notepadsly
 * Version: 1.0
 */

// Define app constant
if (!defined('BAMBOO_APP')) {
    define('BAMBOO_APP', true);
}

require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session
session_start();

// For testing purposes, simulate login if not logged in
if (!isLoggedIn()) {
    // Simulate login for testing - use demohomexx user
    $test_user = fetchRow("SELECT * FROM users WHERE username = 'demohomexx'");
    if ($test_user) {
        $_SESSION['user_id'] = $test_user['id'];
        $_SESSION['username'] = $test_user['username'];
        $_SESSION['user_type'] = 'user';
        $_SESSION['vip_level'] = $test_user['vip_level'];
        $_SESSION['last_activity'] = time();
        echo "<p style='color: blue;'>Simulated login for testing purposes</p>";
    } else {
        die('Test user not found. Please create demohomexx user first.');
    }
}

$current_user = getCurrentUser();
$user_id = $current_user['id'];

echo "<h1>Creating Sample Data for Records Page</h1>";

try {
    $db = getDB();
    
    // Get a sample product
    $product = fetchRow("SELECT * FROM products WHERE status = 'active' LIMIT 1");
    if (!$product) {
        echo "<p style='color: red;'>No active products found. Please add some products first.</p>";
        exit;
    }
    
    echo "<p>Using product: " . htmlspecialchars($product['name']) . "</p>";
    
    // Create a pending task
    $pending_task_sql = "INSERT INTO tasks (user_id, product_id, amount, commission_earned, status, assigned_at, created_at, updated_at)
                         VALUES (?, ?, ?, ?, 'assigned', NOW(), NOW(), NOW())";
    $pending_result = executeQuery($pending_task_sql, [
        $user_id,
        $product['id'],
        $product['price'],
        $product['price'] * 0.05
    ]);

    // Also create an in_progress task
    $in_progress_task_sql = "INSERT INTO tasks (user_id, product_id, amount, commission_earned, status, assigned_at, started_at, created_at, updated_at)
                             VALUES (?, ?, ?, ?, 'in_progress', NOW() - INTERVAL 30 MINUTE, NOW() - INTERVAL 15 MINUTE, NOW(), NOW())";
    $in_progress_result = executeQuery($in_progress_task_sql, [
        $user_id,
        $product['id'],
        $product['price'] * 1.2,
        $product['price'] * 1.2 * 0.05
    ]);
    
    if ($pending_result) {
        echo "<p style='color: green;'>✓ Created pending task</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to create pending task</p>";
    }

    if ($in_progress_result) {
        echo "<p style='color: green;'>✓ Created in_progress task</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to create in_progress task</p>";
    }
    
    // Create a completed task for today
    $completed_task_sql = "INSERT INTO tasks (user_id, product_id, amount, commission_earned, status, assigned_at, completed_at, created_at, updated_at)
                           VALUES (?, ?, ?, ?, 'completed', NOW() - INTERVAL 2 HOUR, NOW() - INTERVAL 30 MINUTE, NOW(), NOW())";
    $completed_result = executeQuery($completed_task_sql, [
        $user_id,
        $product['id'],
        $product['price'],
        $product['price'] * 0.05
    ]);
    
    if ($completed_result) {
        echo "<p style='color: green;'>✓ Created completed task</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to create completed task</p>";
    }
    
    // Create another completed task for pagination testing
    $completed_task2_sql = "INSERT INTO tasks (user_id, product_id, amount, commission_earned, status, assigned_at, completed_at, created_at, updated_at) 
                            VALUES (?, ?, ?, ?, 'completed', NOW() - INTERVAL 2 HOUR, NOW() - INTERVAL 30 MINUTE, NOW(), NOW())";
    $completed_result2 = executeQuery($completed_task2_sql, [
        $user_id, 
        $product['id'], 
        $product['price'] * 1.5, 
        $product['price'] * 1.5 * 0.05
    ]);
    
    if ($completed_result2) {
        echo "<p style='color: green;'>✓ Created second completed task</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to create second completed task</p>";
    }
    
    echo "<h3>Sample Data Created Successfully!</h3>";
    echo "<p><a href='records.php?debug=1'>View Records Page with Debug</a></p>";
    echo "<p><a href='records.php'>View Records Page</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
