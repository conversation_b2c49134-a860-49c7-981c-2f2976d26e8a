<?php
/**
 * Bamboo Direct Task Connection Test
 * Company: Notepadsly
 * Version: 1.0
 * Description: Direct database connection test for task operations without network dependencies
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/functions.php';

// Start session
session_start();

class DirectTaskConnectionTest {
    private $user_id = 9; // Test user ID (demohomexx)
    private $test_results = [];
    
    public function __construct() {
        echo "<h2>Direct Task Connection Test</h2>";
        echo "<p>Testing task operations with direct database connection...</p>";
    }
    
    public function runTests() {
        $this->testDirectDatabaseConnection();
        $this->testDirectTaskCreation();
        $this->testDirectTaskSubmission();
        $this->testDirectTaskCancellation();
        $this->displayResults();
    }
    
    private function testDirectDatabaseConnection() {
        echo "<h3>1. Direct Database Connection Test</h3>";
        
        try {
            $db = getDB();
            $stmt = $db->query("SELECT COUNT(*) as count FROM users");
            $result = $stmt->fetch();
            
            if ($result && $result['count'] > 0) {
                $this->addResult('Database Connection', 'SUCCESS', "Found {$result['count']} users in database");
            } else {
                $this->addResult('Database Connection', 'FAIL', 'No users found in database');
            }
            
            // Test specific user
            $user = fetchRow("SELECT * FROM users WHERE id = ?", [$this->user_id]);
            if ($user) {
                $this->addResult('Test User', 'SUCCESS', "User found: {$user['username']}");
            } else {
                $this->addResult('Test User', 'FAIL', "User with ID {$this->user_id} not found");
            }
            
        } catch (Exception $e) {
            $this->addResult('Database Connection', 'ERROR', $e->getMessage());
        }
    }
    
    private function testDirectTaskCreation() {
        echo "<h3>2. Direct Task Creation Test</h3>";
        
        try {
            // Get user balance
            $balance = getUserBalance($this->user_id);
            if ($balance) {
                $this->addResult('User Balance', 'INFO', "Balance: {$balance['balance']}");
            } else {
                $this->addResult('User Balance', 'FAIL', 'Could not retrieve user balance');
            }

            // Get VIP level
            $vip = getUserVipLevel($this->user_id);
            if ($vip) {
                $this->addResult('VIP Level', 'SUCCESS', "Level {$vip['level']}: {$vip['name']}");
            } else {
                $this->addResult('VIP Level', 'FAIL', 'Could not retrieve VIP level');
            }
            
            // Get available products
            $products = fetchAll("SELECT * FROM products WHERE status = 'active' LIMIT 3");
            if ($products && count($products) > 0) {
                $this->addResult('Products Available', 'SUCCESS', count($products) . ' products found');
                
                // Test task creation logic
                $product = $products[0];
                $task_amount = $product['price'];
                $commission_rate = 5; // 5%
                $commission_earned = ($task_amount * $commission_rate) / 100;
                
                $this->addResult('Task Calculation', 'SUCCESS', "Product: {$product['name']}, Amount: {$task_amount}, Commission: {$commission_earned}");
                
                // Test direct task insertion (without actually inserting)
                $task_data = [
                    'user_id' => $this->user_id,
                    'product_id' => $product['id'],
                    'amount' => $task_amount,
                    'commission_earned' => $commission_earned,
                    'status' => 'assigned'
                ];
                
                $this->addResult('Task Data Preparation', 'SUCCESS', 'Task data prepared successfully');
                
            } else {
                $this->addResult('Products Available', 'FAIL', 'No active products found');
            }
            
        } catch (Exception $e) {
            $this->addResult('Task Creation', 'ERROR', $e->getMessage());
        }
    }
    
    private function testDirectTaskSubmission() {
        echo "<h3>3. Direct Task Submission Test</h3>";
        
        try {
            // Check for existing tasks
            $existing_tasks = fetchAll("SELECT * FROM tasks WHERE user_id = ? AND status = 'assigned' LIMIT 1", [$this->user_id]);
            
            if ($existing_tasks && count($existing_tasks) > 0) {
                $task = $existing_tasks[0];
                $this->addResult('Existing Task Found', 'SUCCESS', "Task ID: {$task['id']}, Amount: {$task['amount']}");
                
                // Test submission logic (without actually submitting)
                $current_balance = getUserBalance($this->user_id);
                $total_return = $task['amount'] + $task['commission_earned'];
                $new_balance = $current_balance['balance'] + $total_return;
                
                $this->addResult('Submission Calculation', 'SUCCESS', "Current: {$current_balance['balance']}, Return: {$total_return}, New: {$new_balance}");
                
            } else {
                $this->addResult('Existing Task', 'INFO', 'No assigned tasks found for testing');
            }
            
            // Test transaction recording logic
            $test_transaction = [
                'user_id' => $this->user_id,
                'type' => 'commission',
                'amount' => 5.00,
                'status' => 'completed',
                'description' => 'Test commission transaction'
            ];
            
            $this->addResult('Transaction Preparation', 'SUCCESS', 'Transaction data prepared');
            
        } catch (Exception $e) {
            $this->addResult('Task Submission', 'ERROR', $e->getMessage());
        }
    }
    
    private function testDirectTaskCancellation() {
        echo "<h3>4. Direct Task Cancellation Test</h3>";
        
        try {
            // Test cancellation logic
            $tasks_to_cancel = fetchAll("SELECT * FROM tasks WHERE user_id = ? AND status = 'assigned' LIMIT 1", [$this->user_id]);
            
            if ($tasks_to_cancel && count($tasks_to_cancel) > 0) {
                $task = $tasks_to_cancel[0];
                $this->addResult('Cancellable Task', 'SUCCESS', "Task ID: {$task['id']} can be cancelled");
                
                // Test cancellation update (without actually updating)
                $update_sql = "UPDATE tasks SET status = 'cancelled' WHERE id = ? AND user_id = ?";
                $this->addResult('Cancellation Query', 'SUCCESS', 'Cancellation query prepared');
                
            } else {
                $this->addResult('Cancellable Task', 'INFO', 'No assigned tasks available for cancellation test');
            }
            
        } catch (Exception $e) {
            $this->addResult('Task Cancellation', 'ERROR', $e->getMessage());
        }
    }
    
    private function addResult($test, $status, $message) {
        $this->test_results[] = [
            'test' => $test,
            'status' => $status,
            'message' => $message
        ];
        
        $color = $this->getStatusColor($status);
        echo "<p style='color: $color;'><strong>$test:</strong> $message</p>";
    }
    
    private function getStatusColor($status) {
        switch ($status) {
            case 'SUCCESS': return 'green';
            case 'FAIL': return 'red';
            case 'ERROR': return 'darkred';
            case 'INFO': return 'blue';
            default: return 'black';
        }
    }
    
    private function displayResults() {
        echo "<h3>Test Summary</h3>";
        
        $success_count = 0;
        $fail_count = 0;
        $error_count = 0;
        $info_count = 0;
        
        foreach ($this->test_results as $result) {
            switch ($result['status']) {
                case 'SUCCESS': $success_count++; break;
                case 'FAIL': $fail_count++; break;
                case 'ERROR': $error_count++; break;
                case 'INFO': $info_count++; break;
            }
        }
        
        echo "<ul>";
        echo "<li style='color: green;'>Successful: $success_count</li>";
        echo "<li style='color: red;'>Failed: $fail_count</li>";
        echo "<li style='color: darkred;'>Errors: $error_count</li>";
        echo "<li style='color: blue;'>Info: $info_count</li>";
        echo "</ul>";
        
        if ($fail_count > 0 || $error_count > 0) {
            echo "<h4>Issues Found:</h4>";
            echo "<ul>";
            foreach ($this->test_results as $result) {
                if ($result['status'] === 'FAIL' || $result['status'] === 'ERROR') {
                    $color = $this->getStatusColor($result['status']);
                    echo "<li style='color: $color;'>{$result['test']}: {$result['message']}</li>";
                }
            }
            echo "</ul>";
        }
        
        echo "<p><strong>Test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
    }
}

// Create API simulation for direct testing
function simulateTaskAPI($action, $data = []) {
    echo "<h3>API Simulation: $action</h3>";
    
    try {
        switch ($action) {
            case 'start_matching':
                echo "<p>Simulating start_matching API call...</p>";
                
                // Simulate the logic from tasks.php
                $user_id = $data['user_id'] ?? 9;
                $user_balance = getUserBalance($user_id);
                $user_vip = getUserVipLevel($user_id);

                if ($user_balance) {
                    echo "<p>User Balance: {$user_balance['balance']}</p>";
                } else {
                    echo "<p>User Balance: Not available</p>";
                }

                if ($user_vip) {
                    echo "<p>VIP Level: {$user_vip['level']}</p>";
                } else {
                    echo "<p>VIP Level: Not available</p>";
                }
                
                // Get random product
                $product = fetchRow("SELECT * FROM products WHERE status = 'active' ORDER BY RAND() LIMIT 1");
                if ($product) {
                    echo "<p>Selected Product: {$product['name']} (Price: {$product['price']})</p>";
                    
                    $commission_rate = 5;
                    $commission_earned = ($product['price'] * $commission_rate) / 100;
                    echo "<p>Commission to earn: {$commission_earned}</p>";
                    
                    echo "<p style='color: green;'>✓ start_matching simulation successful</p>";
                } else {
                    echo "<p style='color: red;'>✗ No products available</p>";
                }
                break;
                
            case 'submit_task':
                echo "<p>Simulating submit_task API call...</p>";
                
                $task_id = $data['task_id'] ?? 1;
                $task = fetchRow("SELECT * FROM tasks WHERE id = ?", [$task_id]);
                
                if ($task) {
                    echo "<p>Task found: ID {$task['id']}, Amount: {$task['amount']}</p>";
                    echo "<p style='color: green;'>✓ submit_task simulation successful</p>";
                } else {
                    echo "<p style='color: red;'>✗ Task not found</p>";
                }
                break;
                
            case 'cancel_task':
                echo "<p>Simulating cancel_task API call...</p>";
                
                $task_id = $data['task_id'] ?? 1;
                $task = fetchRow("SELECT * FROM tasks WHERE id = ?", [$task_id]);
                
                if ($task && $task['status'] === 'assigned') {
                    echo "<p>Task can be cancelled: ID {$task['id']}</p>";
                    echo "<p style='color: green;'>✓ cancel_task simulation successful</p>";
                } else {
                    echo "<p style='color: orange;'>⚠ Task cannot be cancelled or not found</p>";
                }
                break;
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ API simulation error: {$e->getMessage()}</p>";
    }
}

// Run tests
echo "<!DOCTYPE html><html><head><title>Direct Task Connection Test</title></head><body>";

$test = new DirectTaskConnectionTest();
$test->runTests();

echo "<hr>";
echo "<h2>API Simulation Tests</h2>";

simulateTaskAPI('start_matching', ['user_id' => 9]);
simulateTaskAPI('submit_task', ['task_id' => 1]);
simulateTaskAPI('cancel_task', ['task_id' => 1]);

echo "</body></html>";
?>
