<?php
/**
 * Bamboo User Records - Task Records and Submission Page
 * Company: Notepadsly
 * Version: 1.0
 * Description: View and submit pending tasks
 */

// Define app constant
if (!defined('BAMBOO_APP')) {
    define('BAMBOO_APP', true);
}

try {
    // Include required files
    require_once '../../includes/config.php';
    require_once '../../includes/functions.php';

    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in
    if (!isLoggedIn()) {
        redirect('user/login/');
        exit;
    }

    // Get current user information
    $current_user = getCurrentUser();
    if (!$current_user) {
        throw new Exception('Failed to get user information');
    }
    $user_id = $current_user['id'];

    // Initialize default values
    $user_balance = ['balance' => 0, 'commission_balance' => 0, 'frozen_balance' => 0];
    $user_vip = ['level' => 1, 'name' => 'VIP 1', 'max_daily_tasks' => 5];
    $pending_tasks = [];
    $completed_tasks_today = 0;

    // Get user financial data
    try {
        $user_balance = getUserBalance($user_id);
        if (!$user_balance) {
            $user_balance = ['balance' => 0, 'commission_balance' => 0, 'frozen_balance' => 0];
        }
    } catch (Exception $e) {
        logError("Failed to get user balance for user $user_id: " . $e->getMessage());
    }

    // Get user VIP level
    try {
        $user_vip = getUserVipLevel($user_id);
        if (!$user_vip) {
            $user_vip = ['level' => 1, 'name' => 'VIP 1', 'max_daily_tasks' => 5];
        }
    } catch (Exception $e) {
        logError("Failed to get user VIP level for user $user_id: " . $e->getMessage());
    }

    // Get pending tasks
    try {
        $pending_tasks_sql = "SELECT t.*, p.name as product_name, p.image_url, p.price, p.description
                              FROM tasks t
                              LEFT JOIN products p ON t.product_id = p.id
                              WHERE t.user_id = ? AND t.status IN ('assigned', 'in_progress')
                              ORDER BY t.assigned_at ASC";
        $pending_tasks = fetchAll($pending_tasks_sql, [$user_id]);
    } catch (Exception $e) {
        logError("Failed to get pending tasks for user $user_id: " . $e->getMessage());
    }

    // Get completed tasks today
    try {
        $completed_tasks_today = getTasksCompletedToday($user_id);
    } catch (Exception $e) {
        logError("Failed to get completed tasks count for user $user_id: " . $e->getMessage());
    }

    // Get recent completed tasks for history
    try {
        $recent_tasks_sql = "SELECT t.*, p.name as product_name, p.image_url, p.price
                             FROM tasks t
                             LEFT JOIN products p ON t.product_id = p.id
                             WHERE t.user_id = ? AND t.status = 'completed'
                             ORDER BY t.completed_at DESC LIMIT 10";
        $recent_completed_tasks = fetchAll($recent_tasks_sql, [$user_id]);
    } catch (Exception $e) {
        logError("Failed to get recent completed tasks for user $user_id: " . $e->getMessage());
        $recent_completed_tasks = [];
    }

    // Get appearance settings for header
    try {
        $appearance_settings = getAppearanceSettings();
    } catch (Exception $e) {
        logError("Failed to get appearance settings: " . $e->getMessage());
        $appearance_settings = [];
    }

    // Page configuration
    $page_title = 'Task Records';
    $page_description = 'View and submit your pending tasks';
    $page_css = 'records.css';
    $page_js = 'records.js';

} catch (Exception $e) {
    // Critical error - log and show error page
    logError("Critical error in records page: " . $e->getMessage());
    
    echo "<!DOCTYPE html><html><head><title>Error</title></head><body>";
    echo "<h1>System Error</h1>";
    echo "<p>We're experiencing technical difficulties. Please try again later.</p>";
    echo "<p><a href='../dashboard/'>Return to Dashboard</a></p>";
    echo "</body></html>";
    exit;
}

// Include header
include '../includes/user_header.php';
?>

<div class="user-container">
    <!-- Records Page Title -->
    <div class="records-page-title user-fade-in">
        <div class="user-container">
            <h1 class="page-title">Task Records</h1>
            <p class="page-subtitle">View and submit your pending tasks</p>
        </div>
    </div>

    <!-- Task Statistics -->
    <div class="records-stats-container user-slide-in">
        <div class="user-row">
            <div class="user-col-4">
                <div class="stat-card pending-card">
                    <div class="stat-label">Pending Tasks</div>
                    <div class="stat-value"><?php echo count($pending_tasks); ?></div>
                    <div class="stat-note">Tasks awaiting submission</div>
                </div>
            </div>
            <div class="user-col-4">
                <div class="stat-card completed-card">
                    <div class="stat-label">Completed Today</div>
                    <div class="stat-value"><?php echo $completed_tasks_today; ?>/<?php echo $user_vip['max_daily_tasks']; ?></div>
                    <div class="stat-note">Daily progress</div>
                </div>
            </div>
            <div class="user-col-4">
                <div class="stat-card balance-card">
                    <div class="stat-label">Current Balance</div>
                    <div class="stat-value">USDT <?php echo number_format($user_balance['balance'], 2); ?></div>
                    <div class="stat-note">Available balance</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Tasks Section -->
    <?php if (!empty($pending_tasks)): ?>
    <div class="pending-tasks-section user-fade-in">
        <h2 class="section-title">
            <i class="icon-pending"></i>
            Pending Tasks
            <span class="task-count">(<?php echo count($pending_tasks); ?>)</span>
        </h2>
        <p class="section-note">You must submit these tasks before starting new matching sessions.</p>
        
        <div class="tasks-grid">
            <?php foreach ($pending_tasks as $task): ?>
            <div class="task-card pending-task" data-task-id="<?php echo $task['id']; ?>">
                <div class="task-header">
                    <div class="task-status status-<?php echo $task['status']; ?>">
                        <?php echo ucfirst($task['status']); ?>
                    </div>
                    <div class="task-date">
                        <?php echo date('M j, Y H:i', strtotime($task['assigned_at'])); ?>
                    </div>
                </div>
                
                <div class="task-product">
                    <?php if ($task['image_url']): ?>
                    <div class="product-image">
                        <img src="<?php echo htmlspecialchars($task['image_url']); ?>" 
                             alt="<?php echo htmlspecialchars($task['product_name']); ?>"
                             onerror="this.src='../../assets/images/default-product.png'">
                    </div>
                    <?php endif; ?>
                    
                    <div class="product-info">
                        <h3 class="product-name"><?php echo htmlspecialchars($task['product_name']); ?></h3>
                        <div class="product-price">USDT <?php echo number_format($task['amount'], 2); ?></div>
                        <div class="commission-info">
                            Commission: <span class="commission-amount">USDT <?php echo number_format($task['commission_earned'], 2); ?></span>
                        </div>
                    </div>
                </div>
                
                <div class="task-actions">
                    <button class="btn btn-primary submit-task-btn" 
                            data-task-id="<?php echo $task['id']; ?>"
                            data-task-amount="<?php echo $task['amount']; ?>"
                            data-commission="<?php echo $task['commission_earned']; ?>">
                        <i class="icon-submit"></i>
                        Submit Task
                    </button>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php else: ?>
    <div class="no-pending-tasks user-fade-in">
        <div class="empty-state">
            <i class="icon-check-circle"></i>
            <h3>No Pending Tasks</h3>
            <p>You have no pending tasks to submit. You can start new matching sessions.</p>
            <a href="../tasks/" class="btn btn-primary">
                <i class="icon-tasks"></i>
                Start Matching
            </a>
        </div>
    </div>
    <?php endif; ?>

    <!-- Recent Completed Tasks -->
    <?php if (!empty($recent_completed_tasks)): ?>
    <div class="completed-tasks-section user-fade-in">
        <h2 class="section-title">
            <i class="icon-history"></i>
            Recent Completed Tasks
        </h2>
        
        <div class="tasks-list">
            <?php foreach ($recent_completed_tasks as $task): ?>
            <div class="task-item completed-task">
                <div class="task-info">
                    <div class="product-name"><?php echo htmlspecialchars($task['product_name']); ?></div>
                    <div class="task-details">
                        <span class="task-amount">USDT <?php echo number_format($task['amount'], 2); ?></span>
                        <span class="commission-earned">+<?php echo number_format($task['commission_earned'], 2); ?></span>
                        <span class="completion-date"><?php echo date('M j, H:i', strtotime($task['completed_at'])); ?></span>
                    </div>
                </div>
                <div class="task-status-badge status-completed">
                    <i class="icon-check"></i>
                    Completed
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Navigation Actions -->
    <div class="records-actions user-fade-in">
        <div class="action-buttons">
            <a href="../dashboard/" class="btn btn-secondary">
                <i class="icon-dashboard"></i>
                Dashboard
            </a>
            <?php if (empty($pending_tasks)): ?>
            <a href="../tasks/" class="btn btn-primary">
                <i class="icon-tasks"></i>
                Start Matching
            </a>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Task Submission Modal -->
<div id="submitTaskModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Submit Task</h3>
            <span class="modal-close">&times;</span>
        </div>
        <div class="modal-body">
            <div class="task-summary">
                <div class="summary-item">
                    <label>Task Amount:</label>
                    <span id="modalTaskAmount">USDT 0.00</span>
                </div>
                <div class="summary-item">
                    <label>Commission:</label>
                    <span id="modalCommission">USDT 0.00</span>
                </div>
                <div class="summary-item total">
                    <label>Total Return:</label>
                    <span id="modalTotalReturn">USDT 0.00</span>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="cancelSubmit">Cancel</button>
            <button type="button" class="btn btn-primary" id="confirmSubmit">
                <i class="icon-submit"></i>
                Submit Task
            </button>
        </div>
    </div>
</div>

<?php include '../includes/user_footer.php'; ?>
