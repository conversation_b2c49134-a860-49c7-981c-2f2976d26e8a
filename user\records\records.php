<?php
/**
 * Bamboo User Records - Task Records and Submission Page
 * Company: Notepadsly
 * Version: 1.0
 * Description: View and submit pending tasks
 */

// Define app constant
if (!defined('BAMBOO_APP')) {
    define('BAMBOO_APP', true);
}

try {
    // Include required files
    require_once '../../includes/config.php';
    require_once '../../includes/functions.php';

    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in
    if (!isLoggedIn()) {
        redirect('user/login/');
        exit;
    }

    // Get current user information
    $current_user = getCurrentUser();
    if (!$current_user) {
        throw new Exception('Failed to get user information');
    }
    $user_id = $current_user['id'];

    // Initialize default values
    $user_balance = ['balance' => 0, 'commission_balance' => 0, 'frozen_balance' => 0];
    $user_vip = ['level' => 1, 'name' => 'VIP 1', 'max_daily_tasks' => 5];
    $pending_tasks = [];
    $completed_tasks_today = 0;

    // Get user financial data
    try {
        $user_balance = getUserBalance($user_id);
        if (!$user_balance) {
            $user_balance = ['balance' => 0, 'commission_balance' => 0, 'frozen_balance' => 0];
        }
    } catch (Exception $e) {
        logError("Failed to get user balance for user $user_id: " . $e->getMessage());
    }

    // Get user VIP level
    try {
        $user_vip = getUserVipLevel($user_id);
        if (!$user_vip) {
            $user_vip = ['level' => 1, 'name' => 'VIP 1', 'max_daily_tasks' => 5];
        }
    } catch (Exception $e) {
        logError("Failed to get user VIP level for user $user_id: " . $e->getMessage());
    }

    // Get pending tasks with full product information
    $pending_tasks = [];
    try {
        $pending_tasks_sql = "SELECT t.*,
                                     p.name as product_name,
                                     p.image_url,
                                     p.price,
                                     p.description,
                                     p.commission_rate,
                                     pc.name as category
                              FROM tasks t
                              LEFT JOIN products p ON t.product_id = p.id
                              LEFT JOIN product_categories pc ON p.category_id = pc.id
                              WHERE t.user_id = ? AND t.status IN ('assigned', 'in_progress')
                              ORDER BY t.assigned_at ASC
                              LIMIT 1";
        $result = fetchAll($pending_tasks_sql, [$user_id]);
        if ($result !== false && is_array($result)) {
            $pending_tasks = $result;
        }
    } catch (Exception $e) {
        logError("Failed to get pending tasks for user $user_id: " . $e->getMessage());
        $pending_tasks = [];
    }

    // Get completed tasks today
    try {
        $completed_tasks_today = getTasksCompletedToday($user_id);
        if ($completed_tasks_today === false || $completed_tasks_today === null) {
            $completed_tasks_today = 0;
        }
    } catch (Exception $e) {
        logError("Failed to get completed tasks count for user $user_id: " . $e->getMessage());
        $completed_tasks_today = 0;
    }

    // Check for negative balance triggers
    $has_negative_balance = $user_balance['balance'] < 0;
    $next_task_number = $completed_tasks_today + 1;
    $negative_trigger = null;

    try {
        $negative_trigger = fetchRow("SELECT * FROM negative_settings WHERE user_id = ? AND trigger_task_number = ? AND is_active = 1 AND is_triggered = 0", [$user_id, $next_task_number]);
    } catch (Exception $e) {
        logError("Failed to check negative trigger for user $user_id: " . $e->getMessage());
    }

    // Calculate today's profit from completed tasks
    $today_profit = 0;
    try {
        $today_start = date('Y-m-d 00:00:00');
        $today_end = date('Y-m-d 23:59:59');
        $profit_result = fetchRow("SELECT SUM(commission_earned) as total_profit FROM tasks WHERE user_id = ? AND status = 'completed' AND completed_at BETWEEN ? AND ?", [$user_id, $today_start, $today_end]);
        $today_profit = $profit_result['total_profit'] ?? 0;
    } catch (Exception $e) {
        logError("Failed to calculate today's profit for user $user_id: " . $e->getMessage());
        $today_profit = 0;
    }

    // Get completed tasks with pagination
    $page = intval($_GET['page'] ?? 1);
    $per_page = 10;
    $offset = ($page - 1) * $per_page;

    try {
        // Get total count for pagination
        $count_sql = "SELECT COUNT(*) as total FROM tasks WHERE user_id = ? AND status = 'completed'";
        $count_result = fetchRow($count_sql, [$user_id]);
        $total_completed = $count_result['total'] ?? 0;
        $total_pages = ceil($total_completed / $per_page);

        // Get completed tasks for current page
        $recent_tasks_sql = "SELECT t.*, p.name as product_name, p.image_url, p.price, pc.name as category
                             FROM tasks t
                             LEFT JOIN products p ON t.product_id = p.id
                             LEFT JOIN product_categories pc ON p.category_id = pc.id
                             WHERE t.user_id = ? AND t.status = 'completed'
                             ORDER BY t.completed_at DESC
                             LIMIT ? OFFSET ?";
        $recent_completed_tasks = fetchAll($recent_tasks_sql, [$user_id, $per_page, $offset]);
        if (!is_array($recent_completed_tasks)) {
            $recent_completed_tasks = [];
        }
    } catch (Exception $e) {
        logError("Failed to get recent completed tasks for user $user_id: " . $e->getMessage());
        $recent_completed_tasks = [];
        $total_completed = 0;
        $total_pages = 0;
    }

    // Get appearance settings for header and dynamic theming
    try {
        $appearance_settings = getAppearanceSettings();

        // Ensure we have default values
        $appearance_settings = array_merge([
            'primary_color' => '#ff6900',
            'secondary_color' => '#ffffff',
            'accent_color' => '#007bff',
            'gradient_start' => '#ff6900',
            'gradient_end' => '#ff8533'
        ], $appearance_settings);
    } catch (Exception $e) {
        logError("Failed to get appearance settings: " . $e->getMessage());
        $appearance_settings = [
            'primary_color' => '#ff6900',
            'secondary_color' => '#ffffff',
            'accent_color' => '#007bff',
            'gradient_start' => '#ff6900',
            'gradient_end' => '#ff8533'
        ];
    }

    // Page configuration
    $page_title = 'Task Records';
    $page_description = 'View and submit your pending tasks';
    $page_css = 'records.css';
    $page_js = 'records.js';

} catch (Exception $e) {
    // Critical error - log and show error page
    logError("Critical error in records page: " . $e->getMessage());
    
    echo "<!DOCTYPE html><html><head><title>Error</title></head><body>";
    echo "<h1>System Error</h1>";
    echo "<p>We're experiencing technical difficulties. Please try again later.</p>";
    echo "<p><a href='../dashboard/'>Return to Dashboard</a></p>";
    echo "</body></html>";
    exit;
}

// Include header
include '../includes/user_header.php';
?>

<!-- Dynamic Theme CSS Variables -->
<style>
:root {
    --user-primary: <?php echo $appearance_settings['primary_color']; ?> !important;
    --user-secondary: <?php echo $appearance_settings['secondary_color']; ?> !important;
    --user-accent: <?php echo $appearance_settings['accent_color']; ?> !important;
    --user-gradient-start: <?php echo $appearance_settings['gradient_start']; ?> !important;
    --user-gradient-end: <?php echo $appearance_settings['gradient_end']; ?> !important;
    --user-primary-rgb: <?php
        $hex = str_replace('#', '', $appearance_settings['primary_color']);
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));
        echo "$r, $g, $b";
    ?> !important;
}
</style>

<div class="user-container">
    <!-- Records Page Title -->
    <div class="records-page-title user-fade-in">
        <div class="user-container">
            <h1 class="page-title">Task Records</h1>
            <p class="page-subtitle">View and submit your pending tasks</p>
        </div>
    </div>

    <!-- Task Statistics -->
    <div class="records-stats-container user-slide-in">
            <div class="user-row">
                <div class="user-col-4">
                    <div class="stat-card profit-card">
                        <div class="stat-label">Today Profit</div>
                        <div class="stat-value">USDT <?php echo number_format($today_profit, 2); ?></div>
                        <div class="stat-note">Daily Earnings are automatically Updated</div>
                    </div>
                </div>
                <div class="user-col-4">
                    <div class="stat-card completed-card">
                        <div class="stat-label">Completed Today</div>
                        <div class="stat-value"><?php echo $completed_tasks_today; ?>/<?php echo $user_vip['max_daily_tasks']; ?></div>
                        <div class="stat-note">Daily progress</div>
                    </div>
                </div>
                <div class="user-col-4">
                    <div class="stat-card balance-card">
                        <div class="stat-label">Current Balance</div>
                        <div class="stat-value">USDT <?php echo number_format($user_balance['balance'], 2); ?></div>
                        <div class="stat-note">Available balance</div>
                    </div>
                </div>
            </div>
    </div>


    <!-- Pending Tasks Section -->
    <?php if (!empty($pending_tasks)): ?>
    <div class="pending-tasks-section user-fade-in">
            <h2 class="section-title">
                <i class="icon-pending"></i>
                Pending Tasks
                <span class="task-count">(<?php echo count($pending_tasks); ?>)</span>
            </h2>
            <p class="section-note">You must submit these tasks before starting new matching sessions.</p>

            <div class="tasks-grid">
            <?php foreach ($pending_tasks as $task): ?>
            <div class="task-card pending-task" data-task-id="<?php echo $task['id']; ?>">
                <div class="task-header">
                    <div class="task-status status-<?php echo $task['status']; ?>">
                        <?php echo ucfirst($task['status']); ?>
                    </div>
                    <div class="task-date">
                        <?php echo date('M j, Y H:i', strtotime($task['assigned_at'])); ?>
                    </div>
                    <div class="task-actions-header">
                        <?php if ($has_negative_balance): ?>
                        <div class="negative-balance-warning-compact">
                            <i class="icon-warning"></i>
                            <span>Deposit Required</span>
                            <a href="../deposit/" class="btn btn-warning btn-sm">
                                <i class="icon-plus"></i>
                                Deposit
                            </a>
                        </div>
                        <?php else: ?>
                        <button class="btn btn-primary submit-task-btn-header"
                                data-task-id="<?php echo $task['id']; ?>"
                                data-task-amount="<?php echo $task['amount']; ?>"
                                data-commission="<?php echo $task['commission_earned']; ?>">
                            <i class="icon-submit"></i>
                            Submit Task
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="task-product">
                    <?php if ($task['image_url']): ?>
                    <div class="product-image">
                        <img src="<?php echo htmlspecialchars($task['image_url']); ?>" 
                             alt="<?php echo htmlspecialchars($task['product_name']); ?>"
                             onerror="this.src='../../assets/images/default-product.png'">
                    </div>
                    <?php endif; ?>
                    
                    <div class="product-info">
                        <h3 class="product-name"><?php echo htmlspecialchars($task['product_name']); ?></h3>

                        <?php if (!empty($task['category'])): ?>
                        <div class="product-category">Category: <?php echo htmlspecialchars($task['category']); ?></div>
                        <?php endif; ?>

                        <div class="product-price">USDT <?php echo number_format($task['amount'], 2); ?></div>

                        <div class="commission-info">
                            Commission: <span class="commission-amount">USDT <?php echo number_format($task['commission_earned'], 2); ?></span>
                            <span class="commission-rate">(<?php echo number_format($task['commission_rate'], 1); ?>%)</span>
                        </div>

                        <?php if (!empty($task['description'])): ?>
                        <div class="product-description">
                            <?php echo htmlspecialchars(substr($task['description'], 0, 100)); ?>
                            <?php if (strlen($task['description']) > 100): ?>...<?php endif; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

            </div>
            <?php endforeach; ?>
            </div>
        </div>
    </div>
    <?php else: ?>
    <div class="no-pending-tasks user-fade-in">
            <div class="empty-state">
                <i class="icon-check-circle"></i>
                <h3>No Pending Tasks</h3>
                <p>You have no pending tasks to submit. You can start new matching sessions.</p>
                <a href="../tasks/" class="btn btn-primary">
                    <i class="icon-tasks"></i>
                    Start Matching
                </a>
            </div>
    </div>
    <?php endif; ?>

    <!-- Recent Completed Tasks -->
    <div class="completed-tasks-section user-fade-in">
            <h2 class="section-title">
                <i class="icon-history"></i>
                Recent Completed Tasks
                <span class="task-count">(<?php echo $total_completed ?? 0; ?> total)</span>
            </h2>

            <?php if (!empty($recent_completed_tasks)): ?>

            <div class="modern-table-container">
            <table class="modern-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Product</th>
                        <th>Category</th>
                        <th>Amount</th>
                        <th>Commission</th>
                        <th>Completed</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $row_number = $offset + 1;
                    foreach ($recent_completed_tasks as $index => $task):
                        $row_class = ($index % 2 === 0) ? 'even-row' : 'odd-row';
                    ?>
                    <tr class="<?php echo $row_class; ?>">
                        <td class="row-number"><?php echo $row_number++; ?></td>
                        <td class="product-cell">
                            <div class="product-info-table">
                                <?php if ($task['image_url']): ?>
                                <img src="<?php echo htmlspecialchars($task['image_url']); ?>"
                                     alt="<?php echo htmlspecialchars($task['product_name']); ?>"
                                     class="product-thumb"
                                     onerror="this.src='../../assets/images/default-product.png'">
                                <?php endif; ?>
                                <span class="product-name-table"><?php echo htmlspecialchars($task['product_name']); ?></span>
                            </div>
                        </td>
                        <td class="category-cell"><?php echo htmlspecialchars($task['category'] ?? 'N/A'); ?></td>
                        <td class="amount-cell">USDT <?php echo number_format($task['amount'], 2); ?></td>
                        <td class="commission-cell">+<?php echo number_format($task['commission_earned'], 2); ?></td>
                        <td class="date-cell"><?php echo date('M j, Y H:i', strtotime($task['completed_at'])); ?></td>
                        <td class="status-cell">
                            <span class="status-badge status-completed">
                                <i class="icon-check"></i>
                                Completed
                            </span>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <div class="pagination-container">
            <div class="pagination">
                <?php if ($page > 1): ?>
                <a href="?page=<?php echo $page - 1; ?>" class="pagination-btn prev-btn">
                    <i class="icon-chevron-left"></i>
                    Previous
                </a>
                <?php endif; ?>

                <div class="pagination-numbers">
                    <?php
                    $start_page = max(1, $page - 2);
                    $end_page = min($total_pages, $page + 2);

                    if ($start_page > 1): ?>
                        <a href="?page=1" class="pagination-number">1</a>
                        <?php if ($start_page > 2): ?>
                            <span class="pagination-dots">...</span>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                        <a href="?page=<?php echo $i; ?>"
                           class="pagination-number <?php echo $i === $page ? 'active' : ''; ?>">
                            <?php echo $i; ?>
                        </a>
                    <?php endfor; ?>

                    <?php if ($end_page < $total_pages): ?>
                        <?php if ($end_page < $total_pages - 1): ?>
                            <span class="pagination-dots">...</span>
                        <?php endif; ?>
                        <a href="?page=<?php echo $total_pages; ?>" class="pagination-number"><?php echo $total_pages; ?></a>
                    <?php endif; ?>
                </div>

                <?php if ($page < $total_pages): ?>
                <a href="?page=<?php echo $page + 1; ?>" class="pagination-btn next-btn">
                    Next
                    <i class="icon-chevron-right"></i>
                </a>
                <?php endif; ?>
            </div>

            <div class="pagination-info">
                Showing <?php echo $offset + 1; ?>-<?php echo min($offset + $per_page, $total_completed); ?>
                of <?php echo $total_completed; ?> completed tasks
            </div>
        </div>
        <?php endif; ?>

        <?php else: ?>
        <div class="no-completed-tasks">
            <div class="empty-state">
                <i class="icon-history"></i>
                <h3>No Completed Tasks</h3>
                <p>You haven't completed any tasks yet. Start matching to earn commissions!</p>
                <a href="../tasks/" class="btn btn-primary">
                    <i class="icon-tasks"></i>
                    Start Matching
                </a>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Navigation Actions -->
    <div class="records-actions user-fade-in">
            <div class="action-buttons">
            <a href="../dashboard/" class="btn btn-secondary">
                <i class="icon-dashboard"></i>
                Dashboard
            </a>
            <?php if (empty($pending_tasks)): ?>
            <a href="../tasks/" class="btn btn-primary">
                <i class="icon-tasks"></i>
                Start Matching
            </a>
            <?php endif; ?>
            </div>
    </div>
</div>

<!-- Task Submission Modal -->
<div id="submitTaskModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Submit Task</h3>
            <span class="modal-close">&times;</span>
        </div>
        <div class="modal-body">
            <div class="task-summary">
                <div class="summary-item">
                    <label>Task Amount:</label>
                    <span id="modalTaskAmount">USDT 0.00</span>
                </div>
                <div class="summary-item">
                    <label>Commission:</label>
                    <span id="modalCommission">USDT 0.00</span>
                </div>
                <div class="summary-item total">
                    <label>Total Return:</label>
                    <span id="modalTotalReturn">USDT 0.00</span>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="cancelSubmit">Cancel</button>
            <button type="button" class="btn btn-primary" id="confirmSubmit">
                <i class="icon-submit"></i>
                Submit Task
            </button>
        </div>
    </div>
</div>

<?php include '../includes/user_footer.php'; ?>
