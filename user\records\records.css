/**
 * Bamboo User Records - Styles
 * Company: Notepadsly
 * Version: 1.0
 */



/* Records Page Specific Styles */
.records-page-title {
    background: linear-gradient(135deg, var(--user-primary) 0%, var(--user-gradient-end) 100%);
    color: white;
    padding: var(--user-spacing-xl);
    border-radius: var(--user-border-radius-lg);
    margin-bottom: var(--user-spacing-xl);
    box-shadow: var(--user-shadow-lg);
    /* Ensure the header is contained within the user-container */
    margin-left: 0;
    margin-right: 0;
}

.records-page-title .page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-align: center;
}

.records-page-title .page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    text-align: center;
    margin: 0;
}

/* Records Statistics */
.records-stats-container {
    margin-bottom: 2rem;
}

.records-stats-container .user-row {
    display: flex;
    flex-wrap: nowrap;
    gap: 1.5rem;
    margin: 0 !important;
    align-items: stretch;
}

.records-stats-container .user-col-4 {
    flex: 1;
    max-width: calc(33.333% - 1rem);
    margin: 0 !important;
    padding: 0 !important;
}

.stat-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.stat-card.pending-card {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
}

.stat-card.completed-card {
    background: linear-gradient(135deg, #d4edda 0%, #a8e6cf 100%);
}

.stat-card.balance-card {
    background: linear-gradient(135deg, #d1ecf1 0%, #74b9ff 100%);
}

.stat-card.profit-card {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 1px solid #ffc107;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.stat-note {
    font-size: 0.8rem;
    color: #95a5a6;
}

/* Section Titles */
.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-title i {
    color: #667eea;
}

.task-count {
    background: #667eea;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 500;
}

.section-note {
    color: #6c757d;
    margin-bottom: 1.5rem;
    font-style: italic;
}

/* Pending Tasks Section */
.pending-tasks-section {
    margin-bottom: 3rem;
}

.tasks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.task-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.task-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.task-card.pending-task {
    background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.task-actions-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.submit-task-btn-header {
    background: linear-gradient(135deg, var(--user-primary) 0%, var(--user-gradient-end) 100%);
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.submit-task-btn-header:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(var(--user-primary-rgb, 255, 105, 0), 0.3);
}

.negative-balance-warning-compact {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: #fff3cd;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    border: 1px solid #ffc107;
    font-size: 0.85rem;
    color: #856404;
}

.negative-balance-warning-compact .btn {
    padding: 0.25rem 0.75rem;
    font-size: 0.8rem;
}

.task-status {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.task-status.status-assigned {
    background: #fff3cd;
    color: #856404;
}

.task-status.status-in_progress {
    background: #d1ecf1;
    color: #0c5460;
}

.task-date {
    font-size: 0.85rem;
    color: #6c757d;
}

.task-product {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.product-image {
    width: 80px;
    height: 80px;
    border-radius: 0.5rem;
    overflow: hidden;
    flex-shrink: 0;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-info {
    flex: 1;
}

.product-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.product-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.25rem;
}

.commission-info {
    font-size: 0.9rem;
    color: #6c757d;
}

.commission-amount {
    color: #28a745;
    font-weight: 600;
}

.product-brand, .product-category {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.product-discount {
    margin: 0.5rem 0;
}

.original-price {
    text-decoration: line-through;
    color: #6c757d;
    font-size: 0.9rem;
    margin-right: 0.5rem;
}

.discount-badge {
    background: #dc3545;
    color: white;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.product-rating {
    margin: 0.5rem 0;
    font-size: 0.9rem;
}

.rating {
    color: #ffc107;
    font-weight: 600;
    margin-right: 0.5rem;
}

.reviews {
    color: #6c757d;
}

.product-description {
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 0.5rem;
    line-height: 1.4;
}

.task-actions {
    text-align: center;
}

.submit-task-btn {
    background: linear-gradient(135deg, var(--user-primary) 0%, var(--user-gradient-end) 100%);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.submit-task-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(var(--user-primary-rgb, 255, 105, 0), 0.3);
}

/* Negative Balance Warning */
.negative-balance-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 1px solid #ffc107;
    border-radius: 0.75rem;
    padding: 1.5rem;
    text-align: center;
    color: #856404;
}

.negative-balance-warning i {
    font-size: 2rem;
    color: #ffc107;
    margin-bottom: 0.5rem;
    display: block;
}

.negative-balance-warning strong {
    display: block;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: #856404;
}

.negative-balance-warning p {
    margin-bottom: 1rem;
    color: #856404;
}

.negative-balance-warning .btn {
    background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%);
    border: none;
    color: #212529;
    font-weight: 600;
}

/* No Pending Tasks */
.no-pending-tasks,
.no-completed-tasks {
    text-align: center;
    padding: 3rem 1rem;
}

.empty-state i {
    font-size: 4rem;
    color: #28a745;
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-size: 1.5rem;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.empty-state p {
    color: #6c757d;
    margin-bottom: 2rem;
}

/* Completed Tasks Section */
.completed-tasks-section {
    margin-bottom: 3rem;
}

/* Modern Table Styles */
.modern-table-container {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    /* Fix overflow issue */
    overflow-x: auto;
    width: 100%;
    max-width: 100%;
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
    /* Ensure table doesn't overflow */
    min-width: 800px; /* Minimum width to maintain readability */
    table-layout: auto;
}

.modern-table thead {
    background: #ffffff;
    color: #2c3e50;
    border-bottom: 2px solid #e9ecef;
}

.modern-table th {
    padding: 1rem 0.75rem;
    text-align: left;
    font-weight: 600;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.modern-table tbody tr {
    transition: background-color 0.2s ease;
}

.modern-table tbody tr:hover {
    background-color: #f8f9fa;
}

.modern-table tbody tr.even-row {
    background-color: #ffffff;
}

.modern-table tbody tr.odd-row {
    background-color: #f8f9fa;
}

.modern-table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

.row-number {
    font-weight: 600;
    color: var(--user-primary);
    width: 50px;
}

.product-cell {
    min-width: 200px;
}

.product-info-table {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.product-thumb {
    width: 40px;
    height: 40px;
    border-radius: 0.5rem;
    object-fit: cover;
}

.product-name-table {
    font-weight: 600;
    color: #2c3e50;
}

.brand-cell, .category-cell {
    color: #6c757d;
    font-size: 0.85rem;
}

.amount-cell {
    font-weight: 600;
    color: #667eea;
}

.commission-cell {
    font-weight: 600;
    color: #28a745;
}

.date-cell {
    color: #6c757d;
    font-size: 0.85rem;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.status-badge.status-completed {
    background: #d4edda;
    color: #155724;
}

/* Pagination Styles */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.pagination {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pagination-btn {
    padding: 0.5rem 1rem;
    background: #667eea;
    color: white;
    text-decoration: none;
    border-radius: 0.5rem;
    font-size: 0.85rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    transition: all 0.3s ease;
}

.pagination-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

.pagination-numbers {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    margin: 0 1rem;
}

.pagination-number {
    padding: 0.5rem 0.75rem;
    text-decoration: none;
    color: #6c757d;
    border-radius: 0.5rem;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.pagination-number:hover {
    background: #e9ecef;
    color: #2c3e50;
}

.pagination-number.active {
    background: #667eea;
    color: white;
}

.pagination-dots {
    color: #6c757d;
    padding: 0 0.5rem;
}

.pagination-info {
    font-size: 0.85rem;
    color: #6c757d;
}

/* Records Actions */
.records-actions {
    text-align: center;
    padding: 2rem 0;
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Task Submission Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 0;
    border-radius: 1rem;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
}

.modal-close {
    font-size: 1.5rem;
    cursor: pointer;
    color: #6c757d;
    transition: color 0.3s ease;
}

.modal-close:hover {
    color: #dc3545;
}

.modal-body {
    padding: 1.5rem;
}

.task-summary {
    background: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.summary-item:last-child {
    margin-bottom: 0;
}

.summary-item.total {
    border-top: 1px solid #dee2e6;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
    font-weight: 600;
    font-size: 1.1rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .records-stats-container .user-row {
        flex-direction: column;
        gap: 1rem;
    }

    .records-stats-container .user-col-4 {
        max-width: 100%;
        flex: none;
    }

    .tasks-grid {
        grid-template-columns: 1fr;
    }

    .task-product {
        flex-direction: column;
        text-align: center;
    }

    .product-image {
        align-self: center;
    }

    .task-details {
        flex-direction: column;
        gap: 0.25rem;
    }

    .action-buttons {
        flex-direction: column;
        align-items: center;
    }

    .modal-content {
        margin: 5% auto;
        width: 95%;
    }

    .modern-table {
        font-size: 0.8rem;
    }

    .modern-table th,
    .modern-table td {
        padding: 0.5rem 0.25rem;
    }

    .pagination-container {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .pagination {
        justify-content: center;
    }
}

/* Notification Styles - Background Colors Only */
.notification,
.alert,
.toast {
    border: none !important;
    border-left: none !important;
    border-radius: 0.75rem;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.notification.success,
.alert.success,
.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.notification.error,
.alert.error,
.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.notification.warning,
.alert.warning,
.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.notification.info,
.alert.info,
.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}


