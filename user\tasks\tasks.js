/**
 * Bamboo User Dashboard - Tasks Page JavaScript
 * Company: Notepadsly
 * Version: 1.0
 * Description: Core task submission and matchmaking functionality
 */

// Task management object
const TaskManager = {
    config: {
        matchingDuration: 3000, // 3 seconds matching animation
        submissionDelay: 1000,  // 1 second delay for submission
        refreshInterval: 30000  // 30 seconds auto-refresh
    },

    init: function() {
        this.bindEvents();
        this.setupAutoRefresh();
        this.initializeAnimations();
        this.checkPendingTasks();
        console.log('TaskManager initialized');
    },

    bindEvents: function() {
        // Start matching button
        $('#startMatchingBtn').on('click', this.startMatching.bind(this));
        
        // Submit task button
        $('#submitTaskBtn').on('click', this.submitTask.bind(this));
        
        // Cancel task button
        $('#cancelTaskBtn').on('click', this.cancelTask.bind(this));
        
        // Product item clicks (for visual feedback)
        $('.product-item').on('click', this.selectProduct.bind(this));
    },

    initializeAnimations: function() {
        // Animate progress bar on load
        const progressBar = $('.progress-bar');
        if (progressBar.length) {
            const width = progressBar.css('width');
            progressBar.css('width', '0%').animate({ width: width }, 1000);
        }

        // Animate stat values
        $('.stat-value').each(function() {
            const $this = $(this);
            const text = $this.text();
            const match = text.match(/USDT\s*([\d,]+\.?\d*)/);
            if (match) {
                const value = parseFloat(match[1].replace(/,/g, ''));
                $this.text('USDT 0.00');
                $({ value: 0 }).animate({ value: value }, {
                    duration: 1500,
                    step: function() {
                        $this.text('USDT ' + this.value.toFixed(2));
                    },
                    complete: function() {
                        $this.text(text);
                    }
                });
            }
        });
    },

    checkPendingTasks: function() {
        // Check if user has pending tasks that need to be submitted
        $.ajax({
            url: '../api/tasks.php',
            method: 'GET',
            data: { action: 'active_tasks' },
            dataType: 'json',
            success: (response) => {
                if (response.success && response.data && response.data.length > 0) {
                    // User has pending tasks - store them and show view button
                    this.pendingTasks = response.data;
                    this.showPendingTasksNotification(response.data);
                    this.showViewPendingTaskButton();
                } else {
                    // No pending tasks, enable matching
                    this.pendingTasks = null;
                    this.enableMatching();
                }
            },
            error: () => {
                console.error('Failed to check pending tasks');
                // Enable matching anyway to avoid blocking user
                this.pendingTasks = null;
                this.enableMatching();
            }
        });
    },

    showPendingTasksNotification: function(pendingTasks) {
        // Show notification about pending tasks
        const taskCount = pendingTasks.length;
        const notificationHTML = `
            <div class="pending-tasks-alert" style="
                background: linear-gradient(135deg, #FF9800, #F57C00);
                color: white;
                padding: 20px;
                border-radius: 12px;
                margin: 20px 0;
                text-align: center;
                box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
                animation: slideInDown 0.5s ease-out;
            ">
                <h4 style="margin: 0 0 10px 0;">⚠️ Pending Tasks Found</h4>
                <p style="margin: 0 0 15px 0;">
                    You have ${taskCount} pending task${taskCount > 1 ? 's' : ''} that need${taskCount > 1 ? '' : 's'} to be submitted before starting new matching sessions.
                </p>
                <a href="../records/records.php" class="btn btn-light" style="
                    background: white;
                    color: #FF9800;
                    padding: 10px 25px;
                    border-radius: 6px;
                    text-decoration: none;
                    font-weight: bold;
                    display: inline-block;
                ">📋 Complete Pending Tasks</a>
            </div>
        `;

        // Add animation styles
        if (!$('#pending-alert-styles').length) {
            $('head').append(`
                <style id="pending-alert-styles">
                    @keyframes slideInDown {
                        from { opacity: 0; transform: translateY(-20px); }
                        to { opacity: 1; transform: translateY(0); }
                    }
                </style>
            `);
        }

        // Insert after page header
        $('.page-header, .tasks-page-title').first().after(notificationHTML);
    },

    showViewPendingTaskButton: function() {
        // Change button to show pending task instead of disabling
        $('#startMatchingBtn, .start-matching-btn').prop('disabled', false);
        $('#startMatchingBtn, .start-matching-btn').html('📋 View Pending Task');
        $('#startMatchingBtn, .start-matching-btn').removeClass('user-btn-primary').addClass('user-btn-warning');

        // Update click handler to show pending task popup
        $('#startMatchingBtn, .start-matching-btn').off('click').on('click', (e) => {
            e.preventDefault();
            this.showPendingTaskPopup();
        });

        // Keep matching section visible but indicate pending state
        $('.matching-section').removeClass('disabled').css('opacity', '0.8');
    },

    enableMatching: function() {
        // Enable matching controls
        $('#startMatchingBtn, .start-matching-btn').prop('disabled', false);
        $('#startMatchingBtn, .start-matching-btn').html('<i class="icon-play"></i> Start Matching');
        $('#startMatchingBtn, .start-matching-btn').removeClass('user-btn-warning').addClass('user-btn-primary');

        // Restore original click handler for matching
        $('#startMatchingBtn, .start-matching-btn').off('click').on('click', (e) => {
            this.startMatching(e);
        });

        // Enable matching section
        $('.matching-section').removeClass('disabled').css('opacity', '1');
    },

    showPendingTaskPopup: function() {
        if (!this.pendingTasks || this.pendingTasks.length === 0) {
            UserApp.showNotification('No pending tasks found', 'info');
            return;
        }

        // Get the first pending task
        const task = this.pendingTasks[0];

        // Generate appraisal number if not available
        const appraisalNumber = task.appraisal_number || `APR${new Date().toISOString().slice(2,10).replace(/-/g,'')}${String(task.id).padStart(4, '0')}`;

        // Create the task details popup
        const popupHTML = `
            <div id="pending-task-overlay" style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                z-index: 9999;
                display: flex;
                align-items: center;
                justify-content: center;
                animation: fadeIn 0.3s ease-in;
            ">
                <div class="pending-task-card" style="
                    background: white;
                    padding: 30px;
                    border-radius: 20px;
                    text-align: center;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                    max-width: 500px;
                    width: 90%;
                    position: relative;
                ">
                    <!-- Close button -->
                    <button class="close-popup-btn" style="
                        position: absolute;
                        top: 15px;
                        right: 15px;
                        background: none;
                        border: none;
                        font-size: 24px;
                        cursor: pointer;
                        color: #999;
                        width: 30px;
                        height: 30px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    ">&times;</button>

                    <!-- Product Image -->
                    <div class="task-product-image" style="
                        width: 120px;
                        height: 120px;
                        margin: 0 auto 20px;
                        border-radius: 15px;
                        overflow: hidden;
                        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                    ">
                        ${task.image_url ?
                            `<img src="${task.image_url}" alt="${task.product_name}" style="width: 100%; height: 100%; object-fit: cover;">` :
                            `<div style="width: 100%; height: 100%; background: linear-gradient(135deg, #f8f9fa, #e9ecef); display: flex; align-items: center; justify-content: center; font-size: 40px; color: #6c757d;">📦</div>`
                        }
                    </div>

                    <!-- Task Details -->
                    <h3 style="margin: 0 0 10px 0; color: #333; font-size: 1.4em;">${task.product_name}</h3>
                    <p style="margin: 0 0 20px 0; color: #666; font-size: 0.9em;">Task ID: ${task.id}</p>

                    <div class="task-details-grid" style="
                        background: #f8f9fa;
                        padding: 20px;
                        border-radius: 12px;
                        margin-bottom: 25px;
                    ">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                            <span style="color: #666;">Amount:</span>
                            <strong style="color: #333;">USDT ${parseFloat(task.amount).toFixed(2)}</strong>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                            <span style="color: #666;">Commission:</span>
                            <strong style="color: #28a745;">USDT ${parseFloat(task.commission_earned).toFixed(2)}</strong>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span style="color: #666;">Appraisal No:</span>
                            <strong style="color: #333;">${appraisalNumber}</strong>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="task-actions" style="display: flex; gap: 15px; justify-content: center;">
                        <button class="submit-pending-task-btn" data-task-id="${task.id}" style="
                            background: linear-gradient(135deg, #28a745, #20c997);
                            color: white;
                            padding: 12px 25px;
                            border: none;
                            border-radius: 8px;
                            font-weight: bold;
                            cursor: pointer;
                            flex: 1;
                            max-width: 150px;
                        ">Submit Task</button>
                        <button class="close-pending-task-btn" style="
                            background: #6c757d;
                            color: white;
                            padding: 12px 25px;
                            border: none;
                            border-radius: 8px;
                            font-weight: bold;
                            cursor: pointer;
                            flex: 1;
                            max-width: 150px;
                        ">Close</button>
                    </div>
                </div>
            </div>
        `;

        // Add animation styles if not already added
        if (!$('#pending-task-popup-styles').length) {
            $('head').append(`
                <style id="pending-task-popup-styles">
                    @keyframes fadeIn {
                        from { opacity: 0; }
                        to { opacity: 1; }
                    }
                    @keyframes fadeOut {
                        from { opacity: 1; }
                        to { opacity: 0; }
                    }
                    .submit-pending-task-btn:hover {
                        background: linear-gradient(135deg, #218838, #1e7e34) !important;
                        transform: translateY(-1px);
                    }
                    .close-pending-task-btn:hover {
                        background: #5a6268 !important;
                        transform: translateY(-1px);
                    }
                    .close-popup-btn:hover {
                        color: #333 !important;
                        background: rgba(0,0,0,0.1);
                        border-radius: 50%;
                    }
                </style>
            `);
        }

        // Add popup to page
        $('body').append(popupHTML);

        // Bind event handlers
        $('.close-popup-btn, .close-pending-task-btn').on('click', () => {
            this.closePendingTaskPopup();
        });

        $('.submit-pending-task-btn').on('click', (e) => {
            const taskId = $(e.target).data('task-id');
            this.closePendingTaskPopup();
            this.submitPendingTask(taskId);
        });

        // Close on overlay click
        $('#pending-task-overlay').on('click', (e) => {
            if (e.target.id === 'pending-task-overlay') {
                this.closePendingTaskPopup();
            }
        });
    },

    closePendingTaskPopup: function() {
        $('#pending-task-overlay').css('animation', 'fadeOut 0.3s ease-out');
        setTimeout(() => {
            $('#pending-task-overlay').remove();
        }, 300);
    },

    submitPendingTask: function(taskId) {
        // Show loading state
        UserApp.showNotification('Submitting task...', 'info');

        // Submit the task using the existing submit logic
        this.performSubmitTask(taskId, null);
    },

    startMatching: function(e) {
        e.preventDefault();
        
        const $button = $(e.currentTarget);
        
        // Check if user can start matching
        if (window.TaskData.currentBalance < window.TaskData.minBalance) {
            UserApp.showNotification('Insufficient balance to start matching', 'error');
            return;
        }
        
        if (window.TaskData.tasksCompleted >= window.TaskData.dailyLimit) {
            UserApp.showNotification('Daily task limit reached', 'error');
            return;
        }
        
        // Disable button and show loading
        $button.prop('disabled', true).addClass('user-loading');
        $button.html('<i class="icon-loading"></i> Matching...');
        
        // Start matching animation
        this.showMatchingAnimation();

        // Add delay for animation effect
        setTimeout(() => {
            // Direct database connection - NO API, NO CSRF
            $.ajax({
                url: 'start_matching_simple.php',
                method: 'POST',
                data: {
                    // NO CSRF TOKEN REQUIRED
                },
                dataType: 'json',
                success: (response) => {
                    // Hide matching animation
                    this.hideMatchingAnimation();

                    if (response.success) {
                        if (response.requires_deposit) {
                            // Show negative balance modal
                            this.showNegativeBalanceModal(response.data);
                        } else {
                            // Show success and display the new task
                            UserApp.showNotification('🎯 Perfect match found! Task assigned successfully!', 'success');

                            // Display the assigned task with animation
                            this.displayAssignedTask(response.data);

                            // Update matching button state
                            this.resetMatchingButton($button);
                        }
                    } else {
                        // Check if we need to redirect to records page
                        if (response.redirect_to_records) {
                            UserApp.showNotification(response.message + ' Please complete pending tasks first.', 'warning');
                            setTimeout(() => {
                                window.location.href = '../records/records.php';
                            }, 2000);
                        } else {
                            UserApp.showNotification(response.message || 'Failed to start matching', 'error');
                        }
                        this.resetMatchingButton($button);
                    }
                },
                error: (xhr, status, error) => {
                    // Hide matching animation
                    this.hideMatchingAnimation();

                    console.error('Start matching error:', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText,
                        error: error
                    });

                    let errorMessage = 'Network error occurred';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.status === 0) {
                        errorMessage = 'Connection failed. Please check your internet connection.';
                    } else if (xhr.status >= 500) {
                        errorMessage = 'Server error. Please try again later.';
                    } else if (xhr.status === 404) {
                        errorMessage = 'API endpoint not found.';
                    }

                    UserApp.showNotification(errorMessage, 'error');
                    this.resetMatchingButton($button);
                }
            });
        }, 1500); // 1.5 second delay for animation effect
    },

    showMatchingAnimation: function() {
        // Create and show matching animation overlay
        const animationHTML = `
            <div id="matching-animation-overlay" style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                z-index: 9999;
                display: flex;
                align-items: center;
                justify-content: center;
                animation: fadeIn 0.3s ease-in;
            ">
                <div class="matching-container" style="
                    background: white;
                    padding: 40px;
                    border-radius: 20px;
                    text-align: center;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                    max-width: 400px;
                    width: 90%;
                ">
                    <div class="product-spinner" style="
                        width: 120px;
                        height: 120px;
                        margin: 0 auto 20px;
                        border: 4px solid #f3f3f3;
                        border-top: 4px solid #4CAF50;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 40px;
                    ">🎯</div>
                    <h3 style="margin: 0 0 10px 0; color: #333;">Finding Perfect Match</h3>
                    <p style="margin: 0; color: #666; font-size: 14px;">
                        Analyzing products and selecting the best match for you...
                    </p>
                    <div class="progress-dots" style="margin-top: 20px;">
                        <span class="dot" style="
                            display: inline-block;
                            width: 8px;
                            height: 8px;
                            background: #4CAF50;
                            border-radius: 50%;
                            margin: 0 3px;
                            animation: bounce 1.4s ease-in-out infinite both;
                        "></span>
                        <span class="dot" style="
                            display: inline-block;
                            width: 8px;
                            height: 8px;
                            background: #4CAF50;
                            border-radius: 50%;
                            margin: 0 3px;
                            animation: bounce 1.4s ease-in-out 0.16s infinite both;
                        "></span>
                        <span class="dot" style="
                            display: inline-block;
                            width: 8px;
                            height: 8px;
                            background: #4CAF50;
                            border-radius: 50%;
                            margin: 0 3px;
                            animation: bounce 1.4s ease-in-out 0.32s infinite both;
                        "></span>
                    </div>
                </div>
            </div>
        `;

        // Add animation styles
        if (!$('#matching-animation-styles').length) {
            $('head').append(`
                <style id="matching-animation-styles">
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                    @keyframes bounce {
                        0%, 80%, 100% { transform: scale(0); }
                        40% { transform: scale(1); }
                    }
                    @keyframes fadeIn {
                        from { opacity: 0; }
                        to { opacity: 1; }
                    }
                    @keyframes fadeOut {
                        from { opacity: 1; }
                        to { opacity: 0; }
                    }
                </style>
            `);
        }

        // Add to page
        $('body').append(animationHTML);
    },

    hideMatchingAnimation: function() {
        // Hide matching animation with fade out
        $('#matching-animation-overlay').css('animation', 'fadeOut 0.3s ease-out');
        setTimeout(() => {
            $('#matching-animation-overlay').remove();
        }, 300);
    },

    displayAssignedTask: function(taskData) {
        // Display the newly assigned task with animation
        const taskHTML = `
            <div class="assigned-task-container" style="animation: slideInUp 0.5s ease-out;">
                <div class="task-card active-task" style="
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 25px;
                    border-radius: 15px;
                    margin: 20px 0;
                    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
                ">
                    <div class="task-header" style="display: flex; align-items: center; margin-bottom: 20px;">
                        <div class="task-icon" style="
                            width: 60px;
                            height: 60px;
                            background: rgba(255, 255, 255, 0.2);
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 24px;
                            margin-right: 15px;
                        ">📦</div>
                        <div>
                            <h3 style="margin: 0; font-size: 1.4em;">${taskData.product.name}</h3>
                            <p style="margin: 5px 0 0 0; opacity: 0.9;">Task ID: ${taskData.task_id}</p>
                        </div>
                    </div>

                    <div class="task-details" style="
                        background: rgba(255, 255, 255, 0.1);
                        padding: 15px;
                        border-radius: 10px;
                        margin-bottom: 20px;
                    ">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                            <span>Amount:</span>
                            <strong>USDT ${taskData.task_amount}</strong>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                            <span>Commission:</span>
                            <strong style="color: #4CAF50;">USDT ${taskData.commission_amount}</strong>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>Appraisal No:</span>
                            <strong>${taskData.appraisal_number || this.generateAppraisalNumber()}</strong>
                        </div>
                    </div>

                    <div class="task-actions" style="text-align: center;">
                        <button class="btn btn-light submit-task-btn" data-task-id="${taskData.task_id}" style="
                            background: white;
                            color: #667eea;
                            padding: 12px 30px;
                            border: none;
                            border-radius: 25px;
                            font-weight: bold;
                            margin-right: 10px;
                            cursor: pointer;
                        ">Submit Task</button>
                        <button class="btn btn-outline-light close-task-btn" style="
                            background: transparent;
                            color: white;
                            border: 2px solid white;
                            padding: 12px 30px;
                            border-radius: 25px;
                            font-weight: bold;
                            cursor: pointer;
                        ">Close Task</button>
                    </div>
                </div>
            </div>
        `;

        // Add slide animation styles
        if (!$('#task-animation-styles').length) {
            $('head').append(`
                <style id="task-animation-styles">
                    @keyframes slideInUp {
                        from { opacity: 0; transform: translateY(30px); }
                        to { opacity: 1; transform: translateY(0); }
                    }
                </style>
            `);
        }

        // Replace any existing task display
        $('.task-area, .matching-section').html(taskHTML);

        // Bind event handlers
        $('.submit-task-btn').on('click', (e) => this.submitTask(e));
        $('.close-task-btn').on('click', (e) => this.closeTask(e));
    },

    generateAppraisalNumber: function() {
        // Generate a random appraisal number
        const prefix = 'APR';
        const timestamp = Date.now().toString().slice(-6);
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `${prefix}${timestamp}${random}`;
    },

    closeTask: function(e) {
        e.preventDefault();

        // Show confirmation and redirect to records page
        UserApp.showNotification('Redirecting to Records page to manage your tasks...', 'info');
        setTimeout(() => {
            window.location.href = '../records/records.php';
        }, 1500);
    },

    showNegativeBalanceModal: function(taskData) {
        // Create modal HTML
        const modalHtml = `
            <div class="modal fade" id="negativeBalanceModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-warning text-dark">
                            <h5 class="modal-title">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                Deposit Required
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="text-center mb-4">
                                <img src="${taskData.product_image}" alt="${taskData.product_name}"
                                     class="img-fluid rounded" style="max-height: 200px;">
                                <h4 class="mt-3">${taskData.product_name}</h4>
                                <p class="text-muted">Appraisal No: ${taskData.appraisal_no}</p>
                            </div>

                            <div class="alert alert-warning">
                                <h6><i class="bi bi-info-circle me-2"></i>Negative Balance Detected</h6>
                                <p class="mb-2">Your current balance: <strong>USDT ${taskData.new_balance}</strong></p>
                                <p class="mb-0">You need to deposit funds to continue with this task.</p>
                            </div>

                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border rounded p-3">
                                        <h6>Task Amount</h6>
                                        <strong>USDT ${taskData.amount}</strong>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-3">
                                        <h6>Expected Profit</h6>
                                        <strong class="text-success">USDT ${taskData.commission_earned}</strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                Cancel
                            </button>
                            <button type="button" class="btn btn-primary" onclick="window.location.href='../deposit/deposit.php'">
                                <i class="bi bi-plus-circle me-2"></i>
                                Deposit Now
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        $('#negativeBalanceModal').remove();

        // Add modal to body and show
        $('body').append(modalHtml);
        $('#negativeBalanceModal').modal('show');

        // Reload page when modal is closed to show the active task
        $('#negativeBalanceModal').on('hidden.bs.modal', function() {
            window.location.reload();
        });
    },

    showMatchingAnimation: function() {
        // Add matching animation to product grid
        $('.product-item').each(function(index) {
            setTimeout(() => {
                $(this).addClass('matching-highlight');
            }, index * 200);
        });
        
        // Remove highlights after animation
        setTimeout(() => {
            $('.product-item').removeClass('matching-highlight');
        }, this.config.matchingDuration);
    },

    resetMatchingButton: function($button) {
        $button.prop('disabled', false).removeClass('user-loading');
        $button.html('<i class="icon-play"></i> Start Matching');
    },

    submitTask: function(e) {
        e.preventDefault();

        const $button = $(e.currentTarget);
        const taskId = $button.data('task-id');

        if (!taskId) {
            UserApp.showNotification('Invalid task ID', 'error');
            return;
        }

        // Disable button and show loading immediately
        $button.prop('disabled', true).addClass('user-loading');
        $button.html('<i class="icon-loading"></i> Submitting...');

        // Submit task immediately without confirmation
        this.performSubmitTask(taskId, $button);
    },

    performSubmitTask: function(taskId, $button) {
        // Use simple direct connection - NO API, NO CSRF
        this.submitTaskSimple(taskId, $button);
    },

    submitTaskSimple: function(taskId, $button) {
        // Direct database connection - NO API, NO CSRF
        $.ajax({
            url: 'submit_task_simple.php',
            method: 'POST',
            data: {
                task_id: taskId
                // NO CSRF TOKEN REQUIRED
            },
            dataType: 'json',
            success: (response) => {
                if (response.success) {
                    // Show success animation
                    this.showSubmissionSuccess(response.data);

                    // Update UI with new values
                    this.updateTaskStats(response.data);

                    // Show success message
                    UserApp.showNotification('Task submitted successfully via direct database! Profit: USDT ' + response.data.profit_earned, 'success', 5000);

                    // Handle post-submission workflow
                    this.handlePostSubmissionWorkflow(response.data);

                } else {
                    UserApp.showNotification(response.message || 'Failed to submit task', 'error');
                    this.resetSubmitButton($button);
                }
            },
            error: (xhr, status, error) => {
                console.error('Simple direct submit error:', {
                    status: xhr.status,
                    statusText: xhr.statusText,
                    responseText: xhr.responseText,
                    error: error
                });

                let errorMessage = 'Database connection failed';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }

                UserApp.showNotification(errorMessage, 'error');
                this.resetSubmitButton($button);
            }
        });
    },

    showSubmissionSuccess: function(data) {
        // Create success overlay
        const successOverlay = $(`
            <div class="submission-success-overlay">
                <div class="success-content">
                    <div class="success-icon">
                        <i class="icon-check-circle"></i>
                    </div>
                    <h3>Task Completed!</h3>
                    <div class="profit-earned">
                        <span class="profit-label">Profit Earned:</span>
                        <span class="profit-amount">USDT ${data.profit_earned || '0.00'}</span>
                    </div>
                    <div class="new-balance">
                        <span class="balance-label">New Balance:</span>
                        <span class="balance-amount">USDT ${data.new_balance || '0.00'}</span>
                    </div>
                </div>
            </div>
        `);
        
        $('body').append(successOverlay);
        
        // Remove overlay after 3 seconds
        setTimeout(() => {
            successOverlay.fadeOut(500, function() {
                $(this).remove();
            });
        }, 3000);
    },

    updateTaskStats: function(data) {
        // Update today's profit
        if (data.today_profit !== undefined) {
            $('#todayProfit').text('USDT ' + data.today_profit);
        }
        
        // Update balance
        if (data.new_balance !== undefined) {
            $('#todayBalance').text('USDT ' + data.new_balance);
            UserApp.config.balance = data.new_balance;
        }
        
        // Update task progress
        if (data.tasks_completed !== undefined) {
            $('#taskProgress').text(data.tasks_completed + '/' + window.TaskData.dailyLimit);
            
            // Update progress bar
            const percentage = (data.tasks_completed / window.TaskData.dailyLimit) * 100;
            $('.progress-bar').animate({ width: percentage + '%' }, 500);
        }
    },

    resetSubmitButton: function($button) {
        // Handle case when button is null (called from popup)
        if ($button && $button.length) {
            $button.prop('disabled', false).removeClass('user-loading');
            $button.html('<i class="icon-submit"></i> Submit');
        }
    },

    handlePostSubmissionWorkflow: function(data) {
        // Handle different workflow scenarios after task submission
        const workflowStatus = data.workflow_status;

        if (workflowStatus === 'daily_complete') {
            // User has completed their daily task limit - show completion UI instead of redirecting
            UserApp.showNotification(`Daily limit reached! You completed ${data.tasks_completed}/${data.max_daily_tasks} tasks today.`, 'success', 8000);

            // Show completion banner and update UI
            this.showDailyCompletionUI(data);

            // Hide matching controls since daily limit is reached
            this.hideMatchingControls();

        } else if (workflowStatus === 'negative_trigger_pending') {
            // User has a negative settings trigger for next task - update UI instead of reloading
            UserApp.showNotification('Next task will require additional deposit. Continue matching to proceed.', 'warning', 6000);

            // Update UI to show negative trigger warning
            this.showNegativeTriggerWarning();

            // Enable matching button for the special task
            this.enableMatchingForNegativeTrigger();

        } else if (workflowStatus === 'can_continue') {
            // User can continue normal matching - update UI instead of reloading
            UserApp.showNotification('Great job! You can continue matching for more tasks.', 'success', 4000);

            // Enable matching button for next task
            this.enableMatchingButton();

            // Clear any existing task display to prepare for new task
            this.clearCurrentTaskDisplay();

        } else {
            // Default behavior - enable matching for next task instead of reloading
            UserApp.showNotification('Task completed! Ready for next matching task.', 'success', 3000);
            this.enableMatchingButton();
            this.clearCurrentTaskDisplay();
        }

        // Always refresh pending tasks check after submission
        setTimeout(() => {
            this.checkPendingTasks();
        }, 1000);
    },

    showDailyCompletionUI: function(data) {
        // Show completion UI instead of redirecting
        const completionHTML = `
            <div class="daily-completion-container" style="
                background: linear-gradient(135deg, #4CAF50, #45a049);
                color: white;
                padding: 30px;
                border-radius: 15px;
                text-align: center;
                margin: 20px 0;
                box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
            ">
                <h2 style="margin: 0 0 15px 0;">🎉 Daily Tasks Completed!</h2>
                <p style="font-size: 1.2em; margin: 0 0 20px 0;">
                    Congratulations! You've completed ${data.tasks_completed}/${data.max_daily_tasks} tasks today.<br>
                    Total profit earned: USDT ${data.today_profit}
                </p>
                <div style="margin-top: 20px;">
                    <a href="../dashboard/dashboard.php" class="btn btn-light" style="
                        background: white;
                        color: #4CAF50;
                        padding: 12px 25px;
                        border-radius: 8px;
                        text-decoration: none;
                        font-weight: bold;
                        margin-right: 15px;
                    ">View Dashboard</a>
                    <a href="../records/records.php" class="btn btn-outline-light" style="
                        background: transparent;
                        color: white;
                        border: 2px solid white;
                        padding: 12px 25px;
                        border-radius: 8px;
                        text-decoration: none;
                        font-weight: bold;
                    ">View Records</a>
                </div>
            </div>
        `;

        // Replace the task area with completion UI
        $('.task-area, .matching-section').html(completionHTML);
    },

    hideMatchingControls: function() {
        // Hide matching buttons and controls
        $('.start-matching-btn, .matching-controls').hide();
    },

    showNegativeTriggerWarning: function() {
        // Show warning about negative trigger
        const warningHTML = `
            <div class="negative-trigger-warning" style="
                background: linear-gradient(135deg, #FF9800, #F57C00);
                color: white;
                padding: 20px;
                border-radius: 12px;
                margin: 15px 0;
                text-align: center;
            ">
                <h4 style="margin: 0 0 10px 0;">⚠️ Special High-Value Task Available</h4>
                <p style="margin: 0;">
                    Your next task will be a high-value assignment requiring additional deposit.<br>
                    Ensure you have sufficient balance before proceeding.
                </p>
            </div>
        `;

        $('.task-area').prepend(warningHTML);
    },

    enableMatchingForNegativeTrigger: function() {
        // Enable matching button for negative trigger task
        $('.start-matching-btn').prop('disabled', false).removeClass('user-loading');
        $('.start-matching-btn').html('<i class="icon-play"></i> Start High-Value Task');
    },

    enableMatchingButton: function() {
        // Enable the matching button
        $('.start-matching-btn').prop('disabled', false).removeClass('user-loading');
        $('.start-matching-btn').html('<i class="icon-play"></i> Start Matching');
    },

    clearCurrentTaskDisplay: function() {
        // Clear current task display to prepare for new task
        $('.current-task, .active-task').fadeOut(300);
    },

    loadActiveTask: function() {
        // Load and display active task dynamically
        $.ajax({
            url: 'tasks.php',
            method: 'GET',
            data: { action: 'get_active_task' },
            dataType: 'json',
            success: (response) => {
                if (response.success && response.data) {
                    // Update the task display with new task
                    this.displayActiveTask(response.data);
                } else {
                    // No active task, show matching controls
                    this.showMatchingControls();
                }
            },
            error: () => {
                console.error('Failed to load active task');
            }
        });
    },

    displayActiveTask: function(task) {
        // Display the active task in the UI
        const taskHTML = `
            <div class="active-task-card">
                <h3>Active Task: ${task.product_name}</h3>
                <p>Amount: USDT ${task.amount}</p>
                <p>Commission: USDT ${task.commission_earned}</p>
                <button class="btn btn-primary submit-task-btn" data-task-id="${task.id}">
                    Submit Task
                </button>
            </div>
        `;

        $('.task-area').html(taskHTML);
    },

    showMatchingControls: function() {
        // Show matching controls when no active task
        $('.matching-controls, .start-matching-btn').show();
    },

    cancelTask: function(e) {
        e.preventDefault();

        const $button = $(e.currentTarget);
        const taskId = $button.data('task-id');

        if (!taskId) {
            UserApp.showNotification('Invalid task ID', 'error');
            return;
        }

        // Show notification and redirect to Records page
        UserApp.showNotification('Redirecting to Records page to submit your task...', 'info');

        // Redirect to Records page after short delay
        setTimeout(() => {
            window.location.href = '../records/records.php';
        }, 1500);
    },



    resetCancelButton: function($button) {
        $button.prop('disabled', false).removeClass('user-loading');
        $button.html('<i class="icon-close"></i> Close');
    },

    selectProduct: function(e) {
        const $product = $(e.currentTarget);
        
        // Add selection effect
        $('.product-item').removeClass('selected');
        $product.addClass('selected');
        
        // Show product info (optional enhancement)
        const productId = $product.data('product-id');
        console.log('Product selected:', productId);
    },

    setupAutoRefresh: function() {
        // Auto-refresh task data every 30 seconds
        setInterval(() => {
            this.refreshTaskData();
        }, this.config.refreshInterval);
    },

    refreshTaskData: function() {
        // Only refresh if no active task (to avoid interrupting user)
        if (!window.TaskData.hasActiveTask) {
            $.get('../api/tasks.php', {
                action: 'dashboard_summary'
            })
            .done((response) => {
                if (response.success) {
                    this.updateTaskStats(response.data);
                }
            });
        }
    }
};

// Initialize when page is ready
function initializePage() {
    TaskManager.init();
}

// Simple Modal Functions
const SimpleModal = {
    show: function(title, message, onConfirm) {
        $('#modalTitle').text(title);
        $('#modalMessage').text(message);
        $('#confirmModal').show();

        // Remove previous event handlers
        $('#modalConfirm').off('click');
        $('#modalCancel').off('click');
        $('.modal-overlay').off('click');

        // Add new event handlers
        $('#modalConfirm').on('click', function() {
            SimpleModal.hide();
            if (onConfirm) onConfirm();
        });

        $('#modalCancel').on('click', function() {
            SimpleModal.hide();
        });

        $('.modal-overlay').on('click', function() {
            SimpleModal.hide();
        });
    },

    hide: function() {
        $('#confirmModal').hide();
    }
};

// Export for global access
window.TaskManager = TaskManager;
window.SimpleModal = SimpleModal;
