/**
 * Bamboo User Dashboard - Tasks Page JavaScript
 * Company: Notepadsly
 * Version: 1.0
 * Description: Core task submission and matchmaking functionality
 */

// Task management object
const TaskManager = {
    config: {
        matchingDuration: 3000, // 3 seconds matching animation
        submissionDelay: 1000,  // 1 second delay for submission
        refreshInterval: 30000  // 30 seconds auto-refresh
    },

    init: function() {
        this.bindEvents();
        this.setupAutoRefresh();
        this.initializeAnimations();
        console.log('TaskManager initialized');
    },

    bindEvents: function() {
        // Start matching button
        $('#startMatchingBtn').on('click', this.startMatching.bind(this));
        
        // Submit task button
        $('#submitTaskBtn').on('click', this.submitTask.bind(this));
        
        // Cancel task button
        $('#cancelTaskBtn').on('click', this.cancelTask.bind(this));
        
        // Product item clicks (for visual feedback)
        $('.product-item').on('click', this.selectProduct.bind(this));
    },

    initializeAnimations: function() {
        // Animate progress bar on load
        const progressBar = $('.progress-bar');
        if (progressBar.length) {
            const width = progressBar.css('width');
            progressBar.css('width', '0%').animate({ width: width }, 1000);
        }

        // Animate stat values
        $('.stat-value').each(function() {
            const $this = $(this);
            const text = $this.text();
            const match = text.match(/USDT\s*([\d,]+\.?\d*)/);
            if (match) {
                const value = parseFloat(match[1].replace(/,/g, ''));
                $this.text('USDT 0.00');
                $({ value: 0 }).animate({ value: value }, {
                    duration: 1500,
                    step: function() {
                        $this.text('USDT ' + this.value.toFixed(2));
                    },
                    complete: function() {
                        $this.text(text);
                    }
                });
            }
        });
    },

    startMatching: function(e) {
        e.preventDefault();
        
        const $button = $(e.currentTarget);
        
        // Check if user can start matching
        if (window.TaskData.currentBalance < window.TaskData.minBalance) {
            UserApp.showNotification('Insufficient balance to start matching', 'error');
            return;
        }
        
        if (window.TaskData.tasksCompleted >= window.TaskData.dailyLimit) {
            UserApp.showNotification('Daily task limit reached', 'error');
            return;
        }
        
        // Disable button and show loading
        $button.prop('disabled', true).addClass('user-loading');
        $button.html('<i class="icon-loading"></i> Matching...');
        
        // Start matching animation
        this.showMatchingAnimation();
        
        // Direct database connection - NO API, NO CSRF
        $.ajax({
            url: 'start_matching_simple.php',
            method: 'POST',
            data: {
                // NO CSRF TOKEN REQUIRED
            },
            dataType: 'json',
            success: (response) => {
                if (response.success) {
                    if (response.requires_deposit) {
                        // Show negative balance modal
                        this.showNegativeBalanceModal(response.data);
                    } else {
                        // Show success and reload page to show active task
                        UserApp.showNotification('Task assigned successfully!', 'success');
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    }
                } else {
                    // Check if we need to redirect to records page
                    if (response.redirect_to_records) {
                        UserApp.showNotification(response.message + '. Redirecting to Records page...', 'warning');
                        setTimeout(() => {
                            window.location.href = '../records/records.php';
                        }, 2000);
                    } else {
                        UserApp.showNotification(response.message || 'Failed to start matching', 'error');
                    }
                    this.resetMatchingButton($button);
                }
            },
            error: (xhr, status, error) => {
                console.error('Start matching error:', {
                    status: xhr.status,
                    statusText: xhr.statusText,
                    responseText: xhr.responseText,
                    error: error
                });

                let errorMessage = 'Network error occurred';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.status === 0) {
                    errorMessage = 'Connection failed. Please check your internet connection.';
                } else if (xhr.status >= 500) {
                    errorMessage = 'Server error. Please try again later.';
                } else if (xhr.status === 404) {
                    errorMessage = 'API endpoint not found.';
                }

                UserApp.showNotification(errorMessage, 'error');
                this.resetMatchingButton($button);
            }
        });
    },

    showNegativeBalanceModal: function(taskData) {
        // Create modal HTML
        const modalHtml = `
            <div class="modal fade" id="negativeBalanceModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-warning text-dark">
                            <h5 class="modal-title">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                Deposit Required
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="text-center mb-4">
                                <img src="${taskData.product_image}" alt="${taskData.product_name}"
                                     class="img-fluid rounded" style="max-height: 200px;">
                                <h4 class="mt-3">${taskData.product_name}</h4>
                                <p class="text-muted">Appraisal No: ${taskData.appraisal_no}</p>
                            </div>

                            <div class="alert alert-warning">
                                <h6><i class="bi bi-info-circle me-2"></i>Negative Balance Detected</h6>
                                <p class="mb-2">Your current balance: <strong>USDT ${taskData.new_balance}</strong></p>
                                <p class="mb-0">You need to deposit funds to continue with this task.</p>
                            </div>

                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border rounded p-3">
                                        <h6>Task Amount</h6>
                                        <strong>USDT ${taskData.amount}</strong>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-3">
                                        <h6>Expected Profit</h6>
                                        <strong class="text-success">USDT ${taskData.commission_earned}</strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                Cancel
                            </button>
                            <button type="button" class="btn btn-primary" onclick="window.location.href='../deposit/deposit.php'">
                                <i class="bi bi-plus-circle me-2"></i>
                                Deposit Now
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        $('#negativeBalanceModal').remove();

        // Add modal to body and show
        $('body').append(modalHtml);
        $('#negativeBalanceModal').modal('show');

        // Reload page when modal is closed to show the active task
        $('#negativeBalanceModal').on('hidden.bs.modal', function() {
            window.location.reload();
        });
    },

    showMatchingAnimation: function() {
        // Add matching animation to product grid
        $('.product-item').each(function(index) {
            setTimeout(() => {
                $(this).addClass('matching-highlight');
            }, index * 200);
        });
        
        // Remove highlights after animation
        setTimeout(() => {
            $('.product-item').removeClass('matching-highlight');
        }, this.config.matchingDuration);
    },

    resetMatchingButton: function($button) {
        $button.prop('disabled', false).removeClass('user-loading');
        $button.html('<i class="icon-play"></i> Start Matching');
    },

    submitTask: function(e) {
        e.preventDefault();

        const $button = $(e.currentTarget);
        const taskId = $button.data('task-id');

        if (!taskId) {
            UserApp.showNotification('Invalid task ID', 'error');
            return;
        }

        // Disable button and show loading immediately
        $button.prop('disabled', true).addClass('user-loading');
        $button.html('<i class="icon-loading"></i> Submitting...');

        // Submit task immediately without confirmation
        this.performSubmitTask(taskId, $button);
    },

    performSubmitTask: function(taskId, $button) {
        // Use simple direct connection - NO API, NO CSRF
        this.submitTaskSimple(taskId, $button);
    },

    submitTaskSimple: function(taskId, $button) {
        // Direct database connection - NO API, NO CSRF
        $.ajax({
            url: 'submit_task_simple.php',
            method: 'POST',
            data: {
                task_id: taskId
                // NO CSRF TOKEN REQUIRED
            },
            dataType: 'json',
            success: (response) => {
                if (response.success) {
                    // Show success animation
                    this.showSubmissionSuccess(response.data);

                    // Update UI with new values
                    this.updateTaskStats(response.data);

                    // Show success message
                    UserApp.showNotification('Task submitted successfully via direct database! Profit: USDT ' + response.data.profit_earned, 'success', 5000);

                    // Handle post-submission workflow
                    this.handlePostSubmissionWorkflow(response.data);

                } else {
                    UserApp.showNotification(response.message || 'Failed to submit task', 'error');
                    this.resetSubmitButton($button);
                }
            },
            error: (xhr, status, error) => {
                console.error('Simple direct submit error:', {
                    status: xhr.status,
                    statusText: xhr.statusText,
                    responseText: xhr.responseText,
                    error: error
                });

                let errorMessage = 'Database connection failed';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }

                UserApp.showNotification(errorMessage, 'error');
                this.resetSubmitButton($button);
            }
        });
    },

    showSubmissionSuccess: function(data) {
        // Create success overlay
        const successOverlay = $(`
            <div class="submission-success-overlay">
                <div class="success-content">
                    <div class="success-icon">
                        <i class="icon-check-circle"></i>
                    </div>
                    <h3>Task Completed!</h3>
                    <div class="profit-earned">
                        <span class="profit-label">Profit Earned:</span>
                        <span class="profit-amount">USDT ${data.profit_earned || '0.00'}</span>
                    </div>
                    <div class="new-balance">
                        <span class="balance-label">New Balance:</span>
                        <span class="balance-amount">USDT ${data.new_balance || '0.00'}</span>
                    </div>
                </div>
            </div>
        `);
        
        $('body').append(successOverlay);
        
        // Remove overlay after 3 seconds
        setTimeout(() => {
            successOverlay.fadeOut(500, function() {
                $(this).remove();
            });
        }, 3000);
    },

    updateTaskStats: function(data) {
        // Update today's profit
        if (data.today_profit !== undefined) {
            $('#todayProfit').text('USDT ' + data.today_profit);
        }
        
        // Update balance
        if (data.new_balance !== undefined) {
            $('#todayBalance').text('USDT ' + data.new_balance);
            UserApp.config.balance = data.new_balance;
        }
        
        // Update task progress
        if (data.tasks_completed !== undefined) {
            $('#taskProgress').text(data.tasks_completed + '/' + window.TaskData.dailyLimit);
            
            // Update progress bar
            const percentage = (data.tasks_completed / window.TaskData.dailyLimit) * 100;
            $('.progress-bar').animate({ width: percentage + '%' }, 500);
        }
    },

    resetSubmitButton: function($button) {
        $button.prop('disabled', false).removeClass('user-loading');
        $button.html('<i class="icon-submit"></i> Submit');
    },

    handlePostSubmissionWorkflow: function(data) {
        // Handle different workflow scenarios after task submission
        const workflowStatus = data.workflow_status;

        if (workflowStatus === 'daily_complete') {
            // User has completed their daily task limit
            UserApp.showNotification(`Daily limit reached! You completed ${data.tasks_completed}/${data.max_daily_tasks} tasks today.`, 'info', 8000);

            setTimeout(() => {
                // Redirect to dashboard
                window.location.href = '../dashboard/dashboard.php';
            }, 4000);

        } else if (workflowStatus === 'negative_trigger_pending') {
            // User has a negative settings trigger for next task
            UserApp.showNotification('Next task will require additional deposit. Continue matching to proceed.', 'warning', 6000);

            setTimeout(() => {
                // Reload page to allow new matching
                window.location.reload();
            }, 3000);

        } else if (workflowStatus === 'can_continue') {
            // User can continue normal matching
            UserApp.showNotification('Great job! You can continue matching for more tasks.', 'success', 4000);

            setTimeout(() => {
                // Reload page to allow new matching
                window.location.reload();
            }, 2500);

        } else {
            // Default behavior - reload page
            setTimeout(() => {
                window.location.reload();
            }, 3000);
        }
    },

    cancelTask: function(e) {
        e.preventDefault();

        const $button = $(e.currentTarget);
        const taskId = $button.data('task-id');

        if (!taskId) {
            UserApp.showNotification('Invalid task ID', 'error');
            return;
        }

        // Show notification and redirect to Records page
        UserApp.showNotification('Redirecting to Records page to submit your task...', 'info');

        // Redirect to Records page after short delay
        setTimeout(() => {
            window.location.href = '../records/records.php';
        }, 1500);
    },



    resetCancelButton: function($button) {
        $button.prop('disabled', false).removeClass('user-loading');
        $button.html('<i class="icon-close"></i> Close');
    },

    selectProduct: function(e) {
        const $product = $(e.currentTarget);
        
        // Add selection effect
        $('.product-item').removeClass('selected');
        $product.addClass('selected');
        
        // Show product info (optional enhancement)
        const productId = $product.data('product-id');
        console.log('Product selected:', productId);
    },

    setupAutoRefresh: function() {
        // Auto-refresh task data every 30 seconds
        setInterval(() => {
            this.refreshTaskData();
        }, this.config.refreshInterval);
    },

    refreshTaskData: function() {
        // Only refresh if no active task (to avoid interrupting user)
        if (!window.TaskData.hasActiveTask) {
            $.get('../api/tasks.php', {
                action: 'dashboard_summary'
            })
            .done((response) => {
                if (response.success) {
                    this.updateTaskStats(response.data);
                }
            });
        }
    }
};

// Initialize when page is ready
function initializePage() {
    TaskManager.init();
}

// Simple Modal Functions
const SimpleModal = {
    show: function(title, message, onConfirm) {
        $('#modalTitle').text(title);
        $('#modalMessage').text(message);
        $('#confirmModal').show();

        // Remove previous event handlers
        $('#modalConfirm').off('click');
        $('#modalCancel').off('click');
        $('.modal-overlay').off('click');

        // Add new event handlers
        $('#modalConfirm').on('click', function() {
            SimpleModal.hide();
            if (onConfirm) onConfirm();
        });

        $('#modalCancel').on('click', function() {
            SimpleModal.hide();
        });

        $('.modal-overlay').on('click', function() {
            SimpleModal.hide();
        });
    },

    hide: function() {
        $('#confirmModal').hide();
    }
};

// Export for global access
window.TaskManager = TaskManager;
window.SimpleModal = SimpleModal;
