<?php
define('BAMBOO_APP', true);
require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/functions.php';

echo "Checking users in database:\n";

try {
    $users = fetchAll('SELECT id, username, status FROM users LIMIT 10');
    if ($users) {
        foreach($users as $user) {
            echo "ID: {$user['id']}, Username: {$user['username']}, Status: {$user['status']}\n";
        }
    } else {
        echo "No users found\n";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
